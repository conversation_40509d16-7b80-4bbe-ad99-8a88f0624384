<?php

namespace App\Models;

use App\Models\V1\SchoolUser;
use App\NewSchoolPostRequirementModel;
use App\OnboardingInstructor;
use App\ShortlistInstructorModel;
use App\User;
use Illuminate\Database\Eloquent\Model;

class SchoolReviewApplicants extends Model
{
    protected $table = 'platform_school_review_applicants';
    protected $fillable = ['requirement_id', 'school_id', 'instructor_id', 'status', 'proposed_rate'];

    public function user()
    {
        return $this->belongsTo(OnboardingInstructor::class, 'instructor_id');
    }

    public function requirement()
    {
        return $this->belongsTo(PlatformSchoolRequirements::class, 'requirement_id');
    }

    public function school()
    {
        return $this->belongsTo(SchoolUser::class, 'school_id');
    }
}
