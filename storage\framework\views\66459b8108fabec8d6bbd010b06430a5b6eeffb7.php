<div class="col-lg-12"><label for="comment">Contact info</label></div>
<?php $__currentLoopData = $school_contact_info; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $info): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <div class="row after-add-more" style="margin-left:0 !important;">
        <div class="col-md-3 form-group">
            <input type="text" class="form-control" name="job_title[]" placeholder="Job Title" value="<?php echo e($info->job_title ?? ''); ?>">
        </div>
        <div class="col-md-3 form-group">
            <select class="form-control" name="role_id[]" id="role_id">
                <option value="">Select Role</option>
                <?php $__currentLoopData = $schoolRoles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($role->id); ?>" <?php echo e($info->role_id == $role->id ? 'selected' : ''); ?>><?php echo e($role->name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
        <div class="col-md-3 form-group">
            <input type="text" class="form-control" name="first_name[]" placeholder="First name" value="<?php echo e($info->first_name ?? ''); ?>">
        </div>
        <div class="col-md-3 form-group">
            <input type="text" class="form-control" name="last_name[]" placeholder="Last name" value="<?php echo e($info->last_name ?? ''); ?>">
        </div>
        <div class="col-md-6 form-group">
            <input type="text" class="form-control" name="cemail[]" placeholder="Email" value="<?php echo e($info->email ?? ''); ?>">
        </div>
        <div class="col-md-3 form-group">
            <input type="number" class="form-control" name="phone[]" placeholder="Phone" value="<?php echo e($info->phone_number ?? ''); ?>">
        </div>
        <div class="col-md-1 form-group change">
            <?php if($index == 0): ?>
                <a class="btn btn-success add-more" onclick="add_more()">+</a>
            <?php else: ?>
                <a class="btn btn-danger remove">-</a>
            <?php endif; ?>
        </div>
        
        <div class="col-md-2 form-group change">
            <a class="btn btn-primary send-credentials" data-id="<?php echo e($info->id); ?>" onclick="sendCredentials('<?php echo e(route('admin.sendCredentialsPlatformSchools')); ?>', '<?php echo e($info->id); ?>')">Send Credentials</a>
        </div>
        
    </div>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/schools/tabs/school-user.blade.php ENDPATH**/ ?>