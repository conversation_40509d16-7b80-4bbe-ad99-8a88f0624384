<?php

namespace App\Models\V1;

use App\Schools;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;

class SchoolUser extends Authenticatable
{
    use SoftDeletes;

    protected $table = 'school_users';

    protected $fillable = [
        'school_id',
        'job_title',
        'role_id',
        'first_name',
        'last_name',
        'gender',
        'profile_image',
        'email',
        'phone_number',
        'email_notification',
        'app_notification',
        'password',
        'must_reset_password',
        'reset_password_token',
        'reset_password_expires_at',
        'is_main_admin',
        'status',
        'last_login_at',
        'failed_attempts',
        'locked_until'
    ];

    protected $hidden = [
        'password',
        'reset_password_token',
    ];

    // Relationships
    public function school()
    {
        return $this->belongsTo(Schools::class);
    }

    public function role()
    {
        return $this->belongsTo(SchoolRole::class, 'role_id');
    }

    public function actions()
    {
        return $this->belongsToMany(CoreAction::class, 'school_user_actions', 'user_id', 'action_id');
    }
}
