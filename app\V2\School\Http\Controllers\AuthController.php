<?php

namespace App\V2\School\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\V1\SchoolUser;
use App\Schools;
use App\User;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth as FacadesAuth;
use Illuminate\Support\Facades\Hash as FacadesHash;
use Illuminate\Support\Facades\Session as FacadesSession;
use GuzzleHttp\Client;
use Illuminate\Validation\ValidationException;
use App\V2\Core\Helpers\ApiResponse;

class AuthController extends Controller
{
    /**
     * School Login
     */
    public function login(Request $request)
    {
        try {
            $request->validate([
                'email' => 'required|email',
                'password' => 'required'
            ], [
                'email.required' => 'Email field is required',
                'password.required' => 'Password field is required',
            ]);
        } catch (ValidationException $e) {
            return ApiResponse::error("Validation failed", 422, $e->errors());
        }
        $email = $request->email;
        $password = $request->password;

        $user = SchoolUser::where('email', $email)->first();
        if (!$user) {
            return ApiResponse::error("Login failed, please check email id", 401);
        }

        // Check user type, credentials and status
        if (!FacadesHash::check($password, $user->password)) {
            return ApiResponse::error("Login failed. Please check your password", 401);
        }
        if ($user->status == 'inactive') {
            return ApiResponse::error("Your account is deactivated", 403);
        }
        if ($user->status == 'suspended') {
            return ApiResponse::error("Your account is suspended", 403);
        }
        // Login and session handling
        FacadesSession::forget('userewlogin');
        FacadesAuth::guard('platform_school')->login($user);
        session([
            'schoolloginsession' => [
                'id'    => $user->id,
                'name'  => $user->first_name,
                'email' => $user->email,
                // 'type'  => $user->type
            ]
        ]);

        // Fetch chat token
        $chatToken = null;
        try {
            $baseUrl = config('services.node.url', 'http://localhost:8000');
            $apiUrl  = "$baseUrl/user/token?userId={$user->id}&userType=user";

            $client   = new Client();
            $response = $client->get($apiUrl, ['headers' => ['Accept' => 'application/json']]);

            $chatToken = $response->getBody()->getContents();
            if (!empty($chatToken)) {
                session(['chatToken' => $chatToken]);
            }
        } catch (Exception $e) {
            return ApiResponse::error("Error fetching chat token", 500, config('app.debug') ? [$e->getMessage()] : []);
        }

        session(['user_timezone' => $request->input('timezone')]);

        return ApiResponse::success([
            'user'      => $user,
            'chatToken' => $chatToken
        ], "Login successful");
    }

    /**
     * Get logged in school user profile
     */
    public function profile()
    {
        $sessionData = session('schoolloginsession');

        if (!$sessionData || !isset($sessionData['id'])) {
            return ApiResponse::error("User not logged in", 401);
        }

        $user = SchoolUser::with('school')->find($sessionData['id']);

        if (!$user) {
            return ApiResponse::error("User not found", 404);
        }

        return ApiResponse::success($user, "Profile retrieved successfully");
    }
}
