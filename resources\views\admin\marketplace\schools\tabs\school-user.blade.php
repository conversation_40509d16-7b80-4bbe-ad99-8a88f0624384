<div class="col-lg-12"><label for="comment">Contact info</label></div>
@foreach($school_contact_info as $index => $info)
    <div class="row after-add-more" style="margin-left:0 !important;">
        <div class="col-md-3 form-group">
            <input type="text" class="form-control" name="job_title[]" placeholder="Job Title" value="{{ $info->job_title ?? '' }}">
        </div>
        <div class="col-md-3 form-group">
            <select class="form-control" name="role_id[]" id="role_id">
                <option value="">Select Role</option>
                @foreach($schoolRoles as $role)
                    <option value="{{ $role->id }}" {{ $info->role_id == $role->id ? 'selected' : '' }}>{{ $role->name }}</option>
                @endforeach
            </select>
        </div>
        <div class="col-md-3 form-group">
            <input type="text" class="form-control" name="first_name[]" placeholder="First name" value="{{ $info->first_name ?? '' }}">
        </div>
        <div class="col-md-3 form-group">
            <input type="text" class="form-control" name="last_name[]" placeholder="Last name" value="{{ $info->last_name ?? '' }}">
        </div>
        <div class="col-md-6 form-group">
            <input type="text" class="form-control" name="cemail[]" placeholder="Email" value="{{ $info->email ?? '' }}">
        </div>
        <div class="col-md-3 form-group">
            <input type="number" class="form-control" name="phone[]" placeholder="Phone" value="{{ $info->phone_number ?? '' }}">
        </div>
        <div class="col-md-1 form-group change">
            @if($index == 0)
                <a class="btn btn-success add-more" onclick="add_more()">+</a>
            @else
                <a class="btn btn-danger remove">-</a>
            @endif
        </div>
        {{-- Send Credentials Button --}}
        <div class="col-md-2 form-group change">
            <a class="btn btn-primary send-credentials" data-id="{{ $info->id }}" onclick="sendCredentials('{{ route('admin.sendCredentialsPlatformSchools') }}', '{{ $info->id }}')">Send Credentials</a>
        </div>
        {{-- Send Credentials Button --}}
    </div>
@endforeach