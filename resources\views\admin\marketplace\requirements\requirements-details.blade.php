@extends('admin.layouts.master')

@section('title')
    Requirements Details | Whizara
@endsection

@section('content')
    <?php
    $res = get_permission(session('Adminnewlogin')['type']); ?>
    <!-- MAIN SECTION START -->
    <main class="content">
        <div class="container-fluid p-0">
            {{-- BREADCRUMB START --}}
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item " aria-current="page"><a href="{{ url('admin/k12connections/requirements') }}">Requirements List</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Requirements Details</li>
                    <li class="breadcrumb-item active" aria-current="page"><a href="{{ url('admin/k12connections/add-requirements') }}">Add Requirement</a></li>
                </ol>
            </nav>
            {{-- BREADCRUMB END --}}

            {{-- REQUIREMENTS DETAILS START --}}
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        {{-- Requirement Overview Section --}}
                        <div class="card-header border-bottom">
                            <h5 class="mb-0">Requirements Details</h5>
                        </div>
                        <div class="card-body p-0">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item d-flex">
                                    <span class="col-lg-3 col-md-3 col-4 pl-0">School Name :</span>
                                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{ $data->school->school_name ?? '' }}</h6>

                                    <span class="col-lg-3 col-md-3 col-4 pl-0">District School ID :</span>
                                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{ $data->district_school_id ?? '' }}</h6>
                                </li>
                                <li class="list-group-item d-flex">
                                    <span class="col-lg-3 col-md-3 col-4 pl-0">Class Type :</span>
                                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{ classType($data->class_type) }}</h6>

                                    <span class="col-lg-3 col-md-3 col-4 pl-0">Delivery Mode :</span>
                                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{ ucfirst($data->delivery_mode) }}</h6>
                                </li>
                                <li class="list-group-item d-flex">
                                    <span class="col-lg-3 col-md-3 col-4 pl-0">Subject Area :</span>
                                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{ v1SubjectAreaName($data->subject_area_id) ?? '' }}</h6>

                                    <span class="col-lg-3 col-md-3 col-4 pl-0">Subject :</span>
                                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{ v1SubjectName($data->subject_id) ?? '' }}</h6>
                                </li>
                                <li class="list-group-item d-flex">
                                    <span class="col-lg-3 col-md-3 col-4 pl-0">Capacity :</span>
                                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{ $data->capacity }}</h6>

                                    <span class="col-lg-3 col-md-3 col-4 pl-0">Grade Levels :</span>
                                    <h6 class="col-lg-3 col-md-3 col-8 pr-0 mb-0">{{ rtrim(gradeLevel($data->grade_levels_id), ',') }}</h6>
                                </li>
                                @if($data->requirement_tags)
                                    @php $tags = json_decode($data->requirement_tags, true); @endphp
                                    @if(!empty($tags))
                                        <li class="list-group-item d-flex">
                                            <span class="col-lg-3 col-md-3 col-4 pl-0">Tags:</span>
                                            <h6 class="col-lg-9 col-md-9 col-8 pr-0 mb-0">{{ implode(', ', $tags) }}</h6>
                                        </li>
                                    @endif
                                @endif
                                @if($data->description)
                                    <li class="list-group-item d-flex">
                                        <span class="col-lg-3 col-md-3 col-4 pl-0">Description :</span>
                                        <h6 class="col-lg-9 col-md-3 col-8 pr-0 mb-0">{{ $data->description }}</h6>
                                    </li>
                                @endif
                                @if($data->benefits)
                                    <li class="list-group-item d-flex">
                                        <span class="col-lg-3 col-md-3 col-4 pl-0">Benefits :</span>
                                        <h6 class="col-lg-9 col-md-3 col-8 pr-0 mb-0">{{ $data->benefits }}</h6>
                                    </li>
                                @endif
                            </ul>
                            {{-- Requirement Overview Section End --}}

                            {{-- Class Details Section --}}
                            <ul class="list-group list-group-flush">
                                <div class="card-header border-bottom">
                                    <h5 class="mb-0">Class Details</h5>
                                </div>
                                <li class="list-group-item d-flex">
                                    <span class="col-lg-2 col-md-3 col-4 pl-0">Class Start Date</span>
                                    <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ date('m-d-Y', strtotime($data->start_date)) }}</h6>

                                    <span class="col-lg-2 col-md-3 col-4 pl-0">Class End Date</span>
                                    <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ date('m-d-Y', strtotime($data->end_date)) }}</h6>

                                    <span class="col-lg-2 col-md-3 col-4 pl-0">Time Zone</span>
                                    <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $data->time_zone ?? 'Not Specified' }}</h6>
                                </li>
                                <li class="list-group-item d-flex">
                                    <span class="col-lg-2 col-md-3 col-4 pl-0">Number of Instructional Days :</span>
                                    <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $data->no_instrtructional_days }}</h6>

                                    <span class="col-lg-2 col-md-3 col-4 pl-0">Class Duration :</span>
                                    <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $data->class_duration / 60 }} hr</h6>

                                    <span class="col-lg-2 col-md-3 col-4 pl-0">Number of non-instructional hours :</span>
                                    <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $data->no_non_instructional_hr }} hr</h6>
                                </li>
                            </ul>

                            @if($data->delivery_mode == 'in-person')
                                <ul class="list-group list-group-flush">
                                    <div class="card-header border-bottom">
                                        <h5 class="mb-0">Location</h5>
                                    </div>
                                    <li class="list-group-item d-flex">
                                        <span class="col-lg-2 col-md-3 col-4 pl-0">Street Address :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $data->address }}</h6>

                                        <span class="col-lg-2 col-md-3 col-4 pl-0">City :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $data->city }}</h6>

                                        <span class="col-lg-2 col-md-3 col-4 pl-0">State :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $data->state }}</h6>
                                    </li>
                                    <li class="list-group-item d-flex">
                                        <span class="col-lg-2 col-md-3 col-4 pl-0">Country :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $data->country }}</h6>

                                        <span class="col-lg-2 col-md-3 col-4 pl-0">Lattitude :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $data->lat }}</h6>

                                        <span class="col-lg-2 col-md-3 col-4 pl-0">Longitude :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $data->lng }}</h6>
                                    </li>
                                </ul>
                            @endif
                            <ul class="list-group list-group-flush">
                                <div class="card-header border-bottom">
                                    <h5 class="mb-0">Schedule</h5>
                                </div>
                                @if($data->schedule_type == 'regular')
                                    @php
                                        $schedules = is_string($data->regular_days) ? json_decode($data->regular_days, true) : $data->regular_days;
                                    @endphp
                                    @if(is_array($schedules))
                                        @foreach($schedules as $schedule)
                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-2 col-md-3 col-4 pl-0">Day :</span>
                                                <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $schedule['day'] ?? '' }}</h6>

                                                <span class="col-lg-2 col-md-3 col-4 pl-0">Start Time :</span>
                                                <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $schedule['start_time'] ?? '' }}</h6>

                                                <span class="col-lg-2 col-md-3 col-4 pl-0">End Time :</span>
                                                <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $schedule['end_time'] ?? '' }}</h6>
                                            </li>
                                        @endforeach
                                    @endif
                                @endif

                                @if($data->schedule_type == 'alternating')
                                    @php
                                        $schedules = is_string($data->schedule_1_days) ? json_decode($data->schedule_1_days, true) : $data->schedule_1_days;
                                    @endphp
                                    @if(is_array($schedules))
                                        @foreach($schedules as $schedule)
                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-2 col-md-3 col-4 pl-0">Day :</span>
                                                <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $schedule['day'] ?? '' }}</h6>

                                                <span class="col-lg-2 col-md-3 col-4 pl-0">Start Time :</span>
                                                <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $schedule['start_time'] ?? '' }}</h6>

                                                <span class="col-lg-2 col-md-3 col-4 pl-0">End Time :</span>
                                                <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $schedule['end_time'] ?? '' }}</h6>
                                            </li>
                                        @endforeach
                                    @endif
                                @endif
                            </ul>
                            {{-- Class Details Section End --}}

                            {{-- Screenshots Section --}}
                            @if($data->sch_cal_screenshot || $data->district_cal_screenshot || $data->teacher_schedule_screenshot)
                                <ul class="list-group list-group-flush">
                                    <div class="card-header border-bottom">
                                        <h5 class="mb-0">Screenshots</h5>
                                    </div>
                                    <li class="list-group-item d-flex">
                                        <span class="col-lg-2 col-md-3 col-4 pl-0">School Calendar :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">
                                            <a target="_blank" href="{{ generateSignedUrl($data->sch_cal_screenshot) }}">
                                                <img src="{{ url('fileimg.png') }}" height="50px;" width="50px;">
                                            </a>
                                        </h6>

                                        <span class="col-lg-2 col-md-3 col-4 pl-0">District Calendar :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">
                                            <a target="_blank" href="{{ generateSignedUrl($data->district_cal_screenshot) }}">
                                                <img src="{{ url('fileimg.png') }}" height="50px;" width="50px;">
                                            </a>
                                            </h6>

                                        <span class="col-lg-2 col-md-3 col-4 pl-0">Teacher Schedule :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">
                                            <a target="_blank" href="{{ generateSignedUrl($data->teacher_schedule_screenshot) }}">
                                                <img src="{{ url('fileimg.png') }}" height="50px;" width="50px;">
                                            </a>
                                        </h6>
                                    </li>
                                </ul>
                            @endif
                            {{-- Screenshots Section --}}

                            {{-- Others Section --}}
                            <ul class="list-group list-group-flush">
                                <div class="card-header border-bottom">
                                    <h5 class="mb-0">Others</h5>
                                </div>
                                <li class="list-group-item d-flex">
                                    <span class="col-lg-2 col-md-3 col-4 pl-0">Experience</span>
                                    <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $data->experience }}</h6>

                                    <span class="col-lg-2 col-md-3 col-4 pl-0">Qualifications :</span>
                                    <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $data->qualifications }}</h6>

                                    <span class="col-lg-2 col-md-3 col-4 pl-0">Total Budget :</span>
                                    <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $data->total_budget }} $</h6>
                                </li>
                            </ul>
                            {{-- Others Section End --}}

                            {{-- Proctor Section --}}
                            @if($data->proctor)
                                <ul class="list-group list-group-flush">
                                    <div class="card-header border-bottom">
                                        <h5 class="mb-0">Proctor</h5>
                                    </div>
                                    <li class="list-group-item d-flex">
                                        <span class="col-lg-2 col-md-3 col-4 pl-0">Proctor Name :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $data->proctor->proctor_name ?? '' }}</h6>

                                        <span class="col-lg-2 col-md-3 col-4 pl-0">Proctor Email :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $data->proctor->email ?? '' }}</h6>

                                        <span class="col-lg-2 col-md-3 col-4 pl-0">Proctor Phone :</span>
                                        <h6 class="col-lg-2 col-md-3 col-8 pr-0 mb-0">{{ $data->proctor->phone ?? '' }}</h6>
                                    </li>
                                </ul>
                            @endif
                            {{-- Proctor Section End --}}

                            {{-- Meeting Links Section --}}
                            @if($data->meetingLinks)
                                <ul class="list-group list-group-flush">
                                    <div class="card-header border-bottom">
                                        <h5 class="mb-0">Meeting Links</h5>
                                    </div>
                                    @foreach($data->meetingLinks as $link)
                                        <li class="list-group-item d-flex">
                                            <span class="col-lg-3 col-md-3 col-4 pl-0">Link :</span>
                                            <h6 class="col-lg-9 col-md-3 col-8 pr-0 mb-0">{{ $link->link }}</h6>
                                        </li>
                                    @endforeach
                                </ul>
                            @endif
                            {{-- Meeting Links Section End --}}
                        </div>
                    </div>
                </div>
            </div>
            {{-- REQUIREMENTS DETAILS END --}}
            <!-- Nav tabs -->
            <ul class="nav nav-tabs" id="requirementTabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="invites-tab" data-toggle="tab" href="#invites" role="tab" aria-controls="invites" aria-selected="true">
                    Invites
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="proposals-tab" data-toggle="tab" href="#proposals" role="tab" aria-controls="proposals" aria-selected="false">
                    Proposals
                    </a>
                </li>
            </ul>

            <!-- Tab panes -->
            <div class="tab-content mt-3" id="requirementTabsContent">
                <div class="tab-pane fade show active" id="invites" role="tabpanel" aria-labelledby="invites-tab">
                    @include('admin.marketplace.requirements.tabs.invitation')
                </div>
                <div class="tab-pane fade" id="proposals" role="tabpanel" aria-labelledby="proposals-tab">
                    @include('admin.marketplace.requirements.tabs.proposal')
                </div>
            </div>

        </div>
    </main>

    <!-- MAIN SECTION END -->
@endsection
