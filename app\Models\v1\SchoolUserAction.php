<?php

namespace App\Models\V1;

use Illuminate\Database\Eloquent\Model;

class SchoolUserAction extends Model
{
    protected $table = 'school_user_actions';

    public $timestamps = false;

    protected $fillable = [
        'user_id',
        'action_id',
    ];

    public function user()
    {
        return $this->belongsTo(SchoolUser::class, 'user_id');
    }

    public function action()
    {
        return $this->belongsTo(CoreAction::class, 'action_id');
    }
}
