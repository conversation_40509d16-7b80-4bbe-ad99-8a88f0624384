<?php

namespace App\Models\v1;

use Illuminate\Database\Eloquent\Model;

class BudgetState extends Model
{
    protected $table = 'budget_states_v1';

    protected $fillable = [
        'name',
        'in_person',
        'case_management',
        'bilingual_inc',
        'sped_rec_comp',
    ];

    public function subjectBudgets()
    {
        return $this->hasMany(SubjectBudget::class, 'state_id');
    }
}

php artisan make:migration create_schools_budget_v2_table
php artisan make:migration create_schools_subject_budget_v2_table
