<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests;

// Helpers

use App\Helpers\NotificationHelper;
use App\Helpers\DataTableHelper;
use App\EmailTemplate;
use App\FreeResponseQuestionsModel;
use App\InstructorFifthStepOnboardingModel;
use App\InstructorFirstStepOnboardingModel;
use App\InstructorOnboardingAssessmentModel;
use App\InstructorSecondStepOnboardingModel;
use App\InstructorSixthStepOnboardingModel;
// utils

use DB;
use Hash;
use Mail;
use Auth;
use Session;
use Exception;
use Validator;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB as FacadesDB;
use Illuminate\Support\Facades\Hash as FacadesHash;
use Illuminate\Support\Facades\Mail as FacadesMail;
use Illuminate\Support\Facades\Validator as FacadesValidator;

// Models
use App\OnboardingInstructor;
use App\InstructorThirdStepOnboardingModel;
use App\InstructorSubjectsThirdStepOnboardingModel;
use App\Models\OnboardingInstructorContract;
use App\Models\OnboardingInstructorMarketplaceContract;
use App\Models\v1\UserAvailability;
use App\Models\v1\UserAdditionalSubject;
use App\Models\v1\UserNotificationPreference;
use App\Models\v1\UserOnboardingFinalization;
use App\QuestionsModel;
use App\SampleLesson;

class ManageNewEducatorController extends Controller {

    /**
     * Display a listing of the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $query = UserOnboardingFinalization::query();
            $query->addSelect([
                    'name' => OnboardingInstructor::selectRaw('new_onboarding_instructor.first_name')
                            ->whereColumn('user_onboarding_finalization_v1.user_id', 'new_onboarding_instructor.id')
                            ->limit(1),
                    'email' => OnboardingInstructor::selectRaw('new_onboarding_instructor.email')
                            ->whereColumn('user_onboarding_finalization_v1.user_id', 'new_onboarding_instructor.id')
                            ->limit(1),
                    'state' => OnboardingInstructor::selectRaw('new_onboarding_instructor.state')
                            ->whereColumn('user_onboarding_finalization_v1.user_id', 'new_onboarding_instructor.id')
                            ->limit(1),
                    'city' => OnboardingInstructor::selectRaw('new_onboarding_instructor.city')
                            ->whereColumn('user_onboarding_finalization_v1.user_id', 'new_onboarding_instructor.id')
                            ->limit(1),
                    'add_more_subjects' => UserOnboardingFinalization::selectRaw(
                                "CASE
                                    WHEN user_onboarding_finalization_v1.add_more_subjects = 1 THEN 'Yes'
                                    ELSE 'No'
                                END as add_more_subjects"
                            )
                            ->whereColumn('user_onboarding_finalization_v1.user_id', 'user_onboarding_finalization_v1.user_id')
                            ->limit(1),
                    'availability_time' => UserAvailability::selectRaw(
                                "GROUP_CONCAT(
                                    CONCAT(
                                        day_of_week, ' ',
                                        DATE_FORMAT(start_time, '%h:%i %p'), '-', DATE_FORMAT(end_time, '%h:%i %p')
                                    ) SEPARATOR ',\n'
                                )"
                            )
                            ->whereColumn('user_availability_v1.user_id', 'user_onboarding_finalization_v1.user_id')
                            ->groupBy('user_availability_v1.user_id'),
                    'subjects' => InstructorSubjectsThirdStepOnboardingModel::selectRaw('onboarding_instructor_subjects.sub_subject')
                            ->whereColumn('onboarding_instructor_subjects.user_id', 'user_onboarding_finalization_v1.user_id')
                            ->groupBy('onboarding_instructor_subjects.user_id'),
                    'teaching_preference' => InstructorThirdStepOnboardingModel::selectRaw('onboarding_instructor_teaching_preferences.program_type')
                            ->whereColumn('onboarding_instructor_teaching_preferences.user_id', 'user_onboarding_finalization_v1.user_id')
                            ->limit(1),
                    'format' => InstructorThirdStepOnboardingModel::selectRaw('onboarding_instructor_teaching_preferences.format')
                            ->whereColumn('onboarding_instructor_teaching_preferences.user_id', 'user_onboarding_finalization_v1.user_id')
                            ->limit(1)
                    ]);
            $params = DataTableHelper::getParams($request);
            [$count, $result] = DataTableHelper::applyPagination($query, $params['row'], $params['rowperpage']);

            $result->transform(function ($row) {
                $id = $row->user_id;
                $viewUrl = url("admin/k12connections/manage-educator/{$id}");
                // Decode progress JSON just once
                $progress = $row->progress ?? [];

                $formatStatus = function ($key, $completedValues = ['yes', 'completed']) use ($progress) {
                    $value = strtolower(trim($progress[$key] ?? ''));
                    $completedValues = array_map(fn($v) => strtolower(trim($v)), $completedValues);

                    if ($value === '') {
                        return 'In complete';
                    }

                    return in_array($value, $completedValues, true) ? 'Complete' : 'In complete';
                };

                $row->add_more_subjects_status = $formatStatus('Add More Subjects', ['completed']);
                $row->availability_schedule_status = $formatStatus('Add Availability');
                $row->add_substitutes_status = $formatStatus('Available for substitute requirements');
                $row->add_phone_number_status = $formatStatus('Add Phone Number');
                $row->allow_notifications_status = $formatStatus('Manage notifications');

                $row->action = '
                    <div class="d-flex gap-1">
                        <a href="' . $viewUrl . '" class="btn btn-outline-secondary" title="View">
                            <i class="fa fa-eye"></i>
                        </a>
                    </div>
                ';
                return $row;
            });

            $orderCol = $params['columnName'] ?? null;
            $orderDir = strtolower($params['columnSortOrder'] ?? 'asc');

            $sortableTransformFields = [
                'add_more_subjects_status',
                'availability_schedule_status',
                'add_substitutes_status',
                'add_phone_number_status',
                'allow_notifications_status',
            ];

            if (in_array($orderCol, $sortableTransformFields)) {
                $result = $result->sortBy(function ($row) use ($orderCol) {
                    return $row->{$orderCol} === 'Complete' ? 1 : 0;
                }, SORT_REGULAR, $orderDir === 'desc')->values();
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $result);
        }

        return view('admin.marketplace.educator.index');
    }


    public function show($id) {
        // $instructorId = session('selected_instructor_id');
        // $application_id = decrypt_str($instructorId);

        $educator = UserOnboardingFinalization::with(['instructor', 'additionalSubjects', 'availability', 'notificationPreference', 'locations'])->where('user_id', $id)->firstOrFail();
        // $user = OnboardingInstructor::with(['step1', 'step2', 'step3'])->where("id", $application_id)->first();
        // if (!empty($user)) {
        //     $data["user_first_step"] = InstructorFirstStepOnboardingModel::where('user_id', $application_id)->first();
        //     $data["user_third_step"] = InstructorSecondStepOnboardingModel::with(['education', 'teching', 'otherExper', 'references'])->where('user_id', $application_id)->get();
        //     $data["user_fourth_step"] = InstructorThirdStepOnboardingModel::with(['subjects'])->where("user_id", $application_id)->get();
        //     $data["profile"] = InstructorFifthStepOnboardingModel::where("user_id", $application_id)->first();
        //     $data["question"] = QuestionsModel::orderBy("question_id", "asc")->get();
        //     $data["assessment"] = FreeResponseQuestionsModel::orderBy("id", "asc")->get();
        //     $data["quiz"] = InstructorSixthStepOnboardingModel::where(["user_id" => $application_id])->first();
        //     $data["assessments_ans"] = InstructorOnboardingAssessmentModel::where(["user_id" => $application_id])->first();
        //     $data["whizaraEducator"] = OnboardingInstructorContract::where(["user_id" => $application_id])->first();
        //     $data["marketplaceEducator"] = OnboardingInstructorMarketplaceContract::where(["user_id" => $application_id])->first();
        //     $data["additional_certificates"] = FacadesDB::table("additional_certificates")->where("instructor_id", $application_id)->get();
        //     $data["sample_lesson"]=SampleLesson::where("user_id",$application_id)->get();
        // }

        return view('admin.marketplace.educator.show', compact('educator'));
    }

    public function edit($id) {
        $educator = UserOnboardingFinalization::with(['instructor', 'additionalSubjects', 'availability', 'notificationPreference'])->where('user_id', $id)->first();
        return view('admin.marketplace.educator.edit', compact('educator'));
    }

    public function update(Request $request, $id) {
        $educator = UserOnboardingFinalization::findOrFail($id);
        $data = $request->validate([
            'user_status' => 'required|string',
            'application_start_date' => 'required|date',
            'application_end_date' => 'required|date',
            'instructor_id' => 'required|exists:users,id',
            'notification_preferences' => 'array',
            'additional_subjects' => 'array',
            'availability' => 'array',
        ]);

        $educator->update($data);

        return redirect()->route('admin.marketplace.educator.index')
            ->with('success', 'Educator updated successfully.');
    }

    public function destroy($id) {
        $educator = UserOnboardingFinalization::findOrFail($id);
        $educator->delete();
        return redirect()->route('admin.marketplace.educator.index')
            ->with('success', 'Educator deleted successfully.');
    }

    public function export(Request $request) {
        $educators = UserOnboardingFinalization::with(['instructor', 'additionalSubjects', 'availability'])
            ->get();
        $filename = 'educators_' . Carbon::now()->format('Y_m_d_H_i_s') . '.csv';

        $headers = [
            'Content-type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename=' . $filename,
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0',
        ];
        $handle = fopen('php://output', 'w');
        fputcsv($handle, [
            'ID', 'Name', 'Email', 'Status', 'Application Start Date', 'Application End Date',
            'Notification Preferences', 'Additional Subjects', 'Availability'
        ]);
        foreach ($educators as $educator) {
            fputcsv($handle, [
                $educator->id,
                $educator->instructor->full_name,
                $educator->instructor->email,
                $educator->user_status,
                $educator->application_start_date,
                $educator->application_end_date,
                json_encode($educator->notificationPreferences->pluck('notification_type')),
                json_encode($educator->additionalSubjects->pluck('subject_name')),
                json_encode($educator->availability->pluck('availability_time')),
            ]);
        }
        fclose($handle);
        return response()->stream(function () use ($handle) {
            fclose($handle);
        }, 200, $headers);
    }

    public function updateEducatorSubstitue(Request $request)
    {
        try{
            $validated = $request->validate([
                'is_sub' => 'required|in:0,1,2'
            ]);
            FacadesDB::beginTransaction();

            $educator = UserOnboardingFinalization::where('user_id', $request->user_id)->first();
            if (!$educator) {
                return response()->json(['success' => false, 'message' => 'Educator not found.'], 404);
            }
            $educator->open_to_substitute_opportunity = $validated['is_sub'];
            $educator->save();

            FacadesDB::commit();
            return response()->json(['success' => true, 'message' => 'Substitute status updated.']);
        } catch (Exception $e){
            FacadesDB::rollBack();
            Log::error('Something gone wrong:'.''. $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurd'], 500);
        }
    }
}