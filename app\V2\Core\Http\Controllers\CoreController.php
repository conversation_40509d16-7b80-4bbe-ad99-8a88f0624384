<?php

namespace App\V2\Core\Http\Controllers;

use DB;
use Exception;
use App\Http\Controllers\Controller;
use App\V2\Core\Helpers\ApiResponse;

class CoreController extends Controller
{
    /**
     * Get category-based info from school_management_setting
     */
    public function getCategoryBasedInfo($type)
    {
        try {
            $record = DB::table('school_management_setting')
                ->where('type', $type)
                ->first();

            if (!$record) {
                return ApiResponse::error(
                    ucfirst($type) . " not found.",
                    404
                );
            }

            $values = json_decode($record->value, true);

            if (json_last_error() !== JSON_ERROR_NONE) {

                return ApiResponse::error(
                    "Data for {$type} is corrupted or invalid.",
                    500
                );
            }

            return ApiResponse::success(
                $values,
                ucfirst($type) . " retrieved successfully."
            );
        } catch (Exception $e) {

            return ApiResponse::error(
                "Something went wrong while retrieving {$type}.",
                500,
                config('app.debug') ? [$e->getMessage()] : []
            );
        }
    }
}
