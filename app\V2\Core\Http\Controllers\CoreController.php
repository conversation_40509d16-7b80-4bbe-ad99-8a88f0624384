<?php

namespace App\V2\Core\Http\Controllers;

use DB;
use Exception;
use App\Classes;
use App\StateModel;
use App\Models\k12ConnectionCategorizedData;

use App\Http\Controllers\Controller;
use App\V2\Core\Helpers\ApiResponse;

class CoreController extends Controller
{
    /**
     * Get category-based info from school_management_setting
     * requirement_for | position_type | certificate | language | profile_type | per_hour_range | grades | states | program_type | timezone
     */
    public function getCategoryBasedInfo($type)
    {
        try {
            if($type == 'grades') {
                $grades = Classes::all();
                return ApiResponse::success(
                    $grades,
                    ucfirst($type) . " retrieved successfully."
                );
            } elseif ($type == 'states') {
                $states = StateModel::where(["country_id" => "239"])->where("id", "!=", "60")->get();
                return ApiResponse::success(
                    $states,
                    ucfirst($type) . " retrieved successfully."
                );
            } elseif ($type == 'program_type') {
                $program_type = k12ConnectionCategorizedData::where('type', 'program_type')->get();
                return ApiResponse::success(
                    $program_type,
                    ucfirst($type) . " retrieved successfully."
                );
            } elseif ($type == 'timezone') {
                $timezone = k12ConnectionCategorizedData::where('type', 'timezone')->get();
                return ApiResponse::success(
                    $timezone,
                    ucfirst($type) . " retrieved successfully."
                );
            }
            // requirement_for | position_type | certificate | language | profile_type | per_hour_range
            $record = DB::table('school_management_setting')
                ->where('type', $type)
                ->first();

            if (!$record) {
                return ApiResponse::error(
                    ucfirst($type) . " not found.",
                    404
                );
            }

            $values = json_decode($record->value, true);

            if (json_last_error() !== JSON_ERROR_NONE) {

                return ApiResponse::error(
                    "Data for {$type} is corrupted or invalid.",
                    500
                );
            }

            return ApiResponse::success(
                $values,
                ucfirst($type) . " retrieved successfully."
            );
        } catch (QueryException $e) {
            // Return a 500 server error response with a user-friendly message
            return ApiResponse::error('A database error occurred while saving the budget.', 500, $e);
        } catch (Exception $e) {

            return ApiResponse::error(
                "Something went wrong while retrieving {$type}.",
                500,
                config('app.debug') ? [$e->getMessage()] : []
            );
        }
    }
}
