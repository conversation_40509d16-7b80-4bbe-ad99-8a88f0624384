
<style>
    .tab-content{
        width: 100%;
    }
    .accordion-container {
        width: 100%;
        margin: 20px 0;
    }

    .accordion-item {
        border: 1px solid #ddd;
        border-radius: 6px;
        margin-bottom: 8px;
        overflow: hidden;
        background: #fff;
    }

    .accordion-header {
        background: #f8f9fa;
        padding: 12px 15px;
        cursor: pointer;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .accordion-header:hover {
        background: #e9ecef;
    }

    .accordion-icon {
        font-size: 16px;
        transition: transform 0.3s ease;
    }

    .accordion-item.active .accordion-icon {
        transform: rotate(90deg);
    }

    .accordion-body {
        display: none;
        padding: 15px;
        border-top: 1px solid #ddd;
    }

    .form-check {
        margin-bottom: 8px;
    }

    .form-check-label {
        cursor: pointer;
    }
</style>

<div class="accordion-container" id="manualUsersAccordion">
    <?php $__currentLoopData = $school_contact_info; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="accordion-item">
            <div class="accordion-header">
                <span><?php echo e($user->first_name); ?> (<?php echo e($user->email); ?>)</span>
                <span class="accordion-icon">></span>
            </div>
            <div class="accordion-body">
                <?php $__currentLoopData = $coreActions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coreAction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="<?php echo e($coreAction->id); ?>" name="core_action[<?php echo e($user->id); ?>][]" id="coreAction_<?php echo e($user->id); ?>_<?php echo e($coreAction->id); ?>" <?php if($user->actions->contains($coreAction->id)): ?> checked <?php endif; ?>>
                        <label class="form-check-label" for="coreAction_<?php echo e($user->id); ?>_<?php echo e($coreAction->id); ?>"><?php echo e($coreAction->description); ?></label>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    $(document).ready(function () {
        $("#manualUsersAccordion .accordion-header").on("click", function () {
            const item = $(this).closest(".accordion-item");

            // close others
            $("#manualUsersAccordion .accordion-item").not(item).removeClass("active").find(".accordion-body").slideUp();

            // toggle clicked
            item.toggleClass("active");
            item.find(".accordion-body").slideToggle();
        });
    });
</script>

<?php /**PATH D:\whizara\whizara\resources\views/admin/marketplace/schools/tabs/school-user-action.blade.php ENDPATH**/ ?>