{{-- User Action Table --}}
<div class="card">
    <div class="card-header">User Action</div>
    <div class="card-body p-0">
        @if($school_contact_info->count() > 0)
            <div class="table-responsive">
                <table class="table table-striped table-bordered mb-0">
                    <thead class="thead-dark">
                        <tr>
                            <th scope="col" style="text-align: left;">Job Title</th>
                            <th scope="col" style="text-align: left;">Name</th>
                            <th scope="col" style="text-align: left;">Email</th>
                            <th scope="col" style="text-align: left;">Phone</th>
                            <th scope="col" style="text-align: left;">Roles</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach(@$school_contact_info as $key => $data)
                            <tr>
                                <td>{{$data->job_title}}</td>
                                <td>{{$data->first_name}} {{$data->last_name}}</td>
                                <td>{{$data->email}}</td>
                                <td>{{$data->phone_number}} </td>
                                <td>
                                    <select name="role_id" class="form-control role-select" data-original-role="{{ $data->role_id }}" data-user-id="{{ $data->id }}" id="role_id_{{ $data->id }}">
                                        @foreach($schoolRoles as $role)
                                            <option value="{{ $role->id }}" {{ $data->role_id == $role->id ? 'selected' : '' }}>
                                                {{ $role->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <p class="text-muted m-3">No invites sent yet.</p>
        @endif
    </div>
</div>
@section('scripts')
<script>
    $(document).ready(function() {
        $('.role-select').change(function() {
            var userId = $(this).data('user-id');
            var roleId = $(this).val();
            // var $select = $(this);

            // if ($select.data('original-role') == 1 && roleId != 1) {
            //     alertify.error("You cannot change the Admin role");
            //     $select.val("1");
            //     return false;
            // }

            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: "{{ route('admin.marketplace-updateSchoolUserRoles') }}",
                type: "POST",
                data: {
                    user_id: userId,
                    role_id: roleId
                },
                success: function(response) {
                    if (response.success) {
                        alertify.success(response.message);
                    } else {
                        alertify.error(response.message);
                    }
                }
            });
        });
    });
</script>
@endsection