<?php

namespace App\Http\Controllers\Admin;

use App\AdditionalCertificateSubcategory;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\QuestionsModel;
use App\StateModel;
use App\LastChatModel;
use App\Helpers\NotificationHelper;
use App\EmailTemplate;
use App\RejectOnboardingInstructorModel;
use App\RequestChangeOnboardingInstructorModel;
use App\Helpers\DataTableHelper;
use App\OnboardingInstructor;
use App\InstructorFirstStepOnboardingModel;
use App\InstructorSecondStepOnboardingModel;
use App\InstructorThirdStepOnboardingModel;
use App\InstructorForthStepOnboardingModel;
use App\InstructorFifthStepOnboardingModel;
use App\InstructorSixthStepOnboardingModel;
use App\InstructorSeventhStepOnboardingModel;
use App\OnboardingIinstructorGoogleMapAddress;
use App\InstructorEducationSecondStepOnboardingModel;
use App\Exports\Admin\ExportApplications;
use DB;
use Validator;
use Session;
use App\Http\Requests;
use App\Users;
use App\AssessmentsModel;
use App\UserQuizModel;
use App\scheduledInterview;
use App\BackgroundMedicalModel;
use App\UponCompletionModel;
use App\ViewClassroomModel;
use App\UserUponCompletionNoteModel;
use App\ProfileStatusHistoryModel;
use App\rubric;
use App\user_references;
use App\UserEducationModel;
use App\user_interview_slots;
use App\user_contract;
use App\document_form;
use App\Subject;
use Hash;
use Mail;
use Auth;
use App\AvailabilityModel;
use App\AdministrativeInfoModel;
use App\AvailablityLocationModel;
use App\District;
use App\Exports\Admin\ExportMarketplaceApplicants;
use App\FreeResponseQuestionsModel;
use App\Http\Controllers\AdditionalCertificate;
use App\InstructorOnboardingAssessmentModel;
use App\InstructorSubjectsThirdStepOnboardingModel;
use App\invite_application_recruiter;
use App\Models\InstructorBudgetApprovedModel;
use App\Models\InstructorBudgetLine;
use App\Models\OnboardingInstructorContract;
use App\Models\OnboardingInstructorMarketplaceContract;
use App\Models\PlatformSchoolRequirements;
use App\Models\v1\Subject as V1Subject;
use App\Models\v1\SubjectArea as V1SubjectArea;
use App\Models\v1\SchoolSubjectBudget;
use App\Models\v1\SchoolBudget;
use App\Models\v1\BudgetState;
use App\Models\V1\CoreAction;
use App\Models\V1\SchoolRole;
use App\Models\V1\SchoolUser;
use App\Models\V1\SchoolUserAction;
use App\Models\v1\UserOnboardingFinalization;
use App\SampleLesson;
use App\school_contact_info;
use App\Schools;
use App\User;
use App\StatesTerritoriesModel;
use App\UserFirstStepModel;
use App\UserThirdStepModel;
use Carbon\Carbon;
use Excel;
use Exception;
use Illuminate\Support\Facades\Auth as FacadesAuth;
use Illuminate\Support\Facades\DB as FacadesDB;
use Illuminate\Support\Facades\Facade;
use Illuminate\Support\Facades\Hash as FacadesHash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail as FacadesMail;
use Illuminate\Support\Facades\Validator as FacadesValidator;
use Illuminate\Validation\ValidationException;

class ManageNewInstructorController extends Controller
{
    public function generateChatLink($encryptedId)
    {
        $chatRoute = url('admin/onboarding-instructor-chat/' . $encryptedId);
        return "<a href='{$chatRoute}'><i class='fas fa-comment fa-lg'></i></a>";
    }

    private function generateActionButtons($encryptedStrId, $res)
    {
        // $viewRoute = url('admin/viewonboardinginstructordetails/step1/' . $encryptedStrId);
        $viewRoute = url('admin/k12connections/application/step1/');
        // dd($viewRoute);
        $viewMailRoute = url('view-mail/' . $encryptedStrId);
        $actionUrl = "javascript:void(0);";
        $editButton = $deleteButton = $mailButton = '';

        if (isset($res['manageinstructor'])) :
            if (array_key_exists('manageinstructor', $res)) :
                // if (in_array('update', json_decode($res['manageinstructor'], true))) :
                //     $editRoute = url('edit-onboarding-instructor/' . $encryptedStrId);

                //     $editButton = "<a href='{$editRoute}'><button type='button' class='btn btn-rounded btn-block btn-xs btn-outline-secondary' title='Edit'><i class='fa fa-pencil' aria-hidden='true'></i></button></a>";
                // endif;

                if (in_array('delete', json_decode($res['manageinstructor'], true))) :
                    $deleteButton = "<a class='instructor_delete'  href='{$actionUrl}' data-id='{$encryptedStrId}'><button type='button' title='Delete' class='btn btn-rounded btn-block btn-xs btn-outline-danger'><i class='fa fa-trash' aria-hidden='true'></i></button></a>";

                endif;

                if (in_array('mail', json_decode($res['manageinstructor'], true))) :
                    $mailButton = "<a  href='{$viewMailRoute}' ><button type='button' title='Mail' class='btn btn-rounded btn-block btn-xs btn-outline-secondary'><i class='fa fa-envelope' aria-hidden='true'></i></button></a>";

                endif;

            endif;
        endif;



        //     $viewButton = "
        //     <form action='{$viewRoute}' method='POST' style='display:inline;'>
        //         <input type='hidden' name='_token' value='{$csrfToken}'>
        //         <input type='hidden' name='encryptedStrId' value='{$encryptedStrId}'>
        //         <button type='submit' title='View' class='btn btn-rounded btn-block btn-xs btn-outline-secondary'>
        //             <i class='fa fa-eye' aria-hidden='true'></i>
        //         </button>
        //     </form>
        // ";

        $viewButton = "
            <form action='" . url('admin/k12connections/set-instructor-id') . "' method='POST' style='display:inline;'>
                " . csrf_field() . "
                <input type='hidden' name='instructor_id' value='{$encryptedStrId}'>
                <button type='submit' title='View' class='btn btn-rounded btn-block btn-xs btn-outline-secondary'>
                    <i class='fa fa-eye' aria-hidden='true'></i>
                </button>
            </form>
        ";

        // $viewButton = "<a href='{$viewRoute}'><button type='button' title='View' class='btn btn-rounded btn-block btn-xs btn-outline-secondary'><i class='fa fa-eye' aria-hidden='true'></i></button></a>";

        return "<div class='w-100 d-flex justify-content-around align-items-center'>{$editButton}{$viewButton}{$deleteButton}{$mailButton}</div>";
    }

    private function getEmailSubject($message_type, $company_name = 'Whizara')
    {
        $subject = '';
        switch ($message_type) {
            case 'action_required':
                $subject = "Action Required: Important Message from $company_name";
                break;
            case 'important_notification':
                $subject = "Important Notification: Message from $company_name";
                break;
            case 'reminder':
                $subject = "Reminder: Please Review Your Latest Message";
                break;
            case 'system_alert':
                $subject = "System Alert: Important Update from $company_name";
                break;
            case 'event_update':
                $subject = "Event Update: Important Details Inside";
                break;
            case 'new_feature_announcement':
                $subject = "New Feature: Check Out What's New at $company_name";
                break;
            case 'security_alert':
                $subject = "Security Alert: Important Message Regarding Your Account";
                break;
            case 'general_information':
                $subject = "Information Update: New Message from $company_name";
                break;
            default:
                $subject = "New Message from $company_name";
                break;
        }
        return $subject;
    }

    private function generateStatusButton($status, $id)
    {
        switch ($status) {
            case 'InProgress':
                return '<select data-user-id=' . $id . ' class="form-control" id="userStatus" disabled data-toggle="select2" name="userStatus">
                    <option value="InProgress" selected> Pending </option>
                </select>';
                break;

            case 'UnderReview':
            case 'ChangeRequested':
                return '<select data-user-id=' . $id . ' class="form-control" id="userStatus"
                    data-toggle="select2" name="userStatus">
                        <option value="UnderReview" hidden ' . ($status == 'UnderReview' ? 'selected' : '') . '>Under Review</option>
                        <option value="ChangeRequested" ' . ($status == 'ChangeRequested' ? 'selected hidden' : '') . ' >Request Change</option>
                        <option value="Approved">Approve</option>
                        <option value="Declined">Reject</option>

                </select>';
                break;
            case 'Active':
                return '<select data-user-id=' . $id . ' class="form-control" id="userStatus"
                    data-toggle="select2" name="userStatus">
                    <option value="Active" selected>Activate</option>
                    <option value="Declined">Reject</option>
                </select>';
                break;

            case 'Declined':
                return '<select data-user-id=' . $id . ' class="form-control" id="userStatus"
                    data-toggle="select2" name="userStatus">
                    <option value="ChangeRequested">Request Change</option>
                    <option value="Approved">Approve</option>
                    <option value="Declined" selected>Reject</option>
                </select>';
                break;
            case  'Approved':
                return '<select data-user-id=' . $id . ' class="form-control" id="userStatus"
                    data-toggle="select2" name="userStatus">
                    <option value="Approved" selected>Approved</option>
                    <option value="UnderReview">Withdraw</option>
                    <option value="Declined">Reject</option>
                </select>';

            default:
                # code...
                break;
        }
    }

    private function generateVerifiedButton($status, $id)
    {
        switch ($status) {
            case 0:
                return '<span class="btn btn-danger">Email Not Verified</span>';
            case 1:
                // return '<svg width="40px" height="40px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#08a206">
                //         <g id="SVGRepo_bgCarrier" stroke-width="0"/>
                //         <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/>
                //         <g id="SVGRepo_iconCarrier"> <path d="M4 12.6111L8.92308 17.5L20 6.5" stroke="#08a206" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </g>
                //         </svg>';
                return '<span class="btn btn-success">Email Verified</span>';
            default:
                return '';
        }
    }

    private function isStepPending($pendings, $step)
    {
        foreach ($pendings as $pendingStep) {
            if (strpos($pendingStep, $step) === 0) {
                return true; // Found a match
            }
        }
        return false; // No match found
    }

    public function manageInstructor(Request $request)
    {
        if (get_childpermission(get_permission(session('Adminnewlogin')['type']), 'manageinstructor', 'view') != true) {
            return redirect("/no-permission");
        }

        if ($request->ajax()) {
            $qry = OnboardingInstructor::with(['step1', 'step2', 'step3', 'step5', 'step6', 'step7', 'googleMap', 'rejectInstructor'])->select('new_onboarding_instructor.*')
                ->where("new_onboarding_instructor.type", "=", "5");
            // ->whereIn("user_status", ['InProgress','UnderReview','ChangeRequested','Active','Declined']);

            $id = $request->id;
            switch ($id) {

                case "ALL":
                    $qry->whereIn("user_status", ['InProgress', 'UnderReview', 'ChangeRequested', 'Active', 'Declined', 'Withdraw', 'Approved']);
                    break;

                case "InProgress":
                    $qry->where("user_status", "InProgress");
                    break;

                case "UnderReview":
                    $qry->where("user_status", "UnderReview");
                    break;

                case "ChangeRequested":
                    $qry->where("user_status", "ChangeRequested");
                    break;

                case "Active":
                    $qry->where("user_status", "Active")
                        ->with(['whizaraContract', 'marketplaceContract'])
                        ->where(function ($query) {
                            $query->whereHas('whizaraContract')
                                ->orWhereHas('marketplaceContract');
                        });
                    break;

                case "Declined":
                    $qry->where("user_status", "Declined");
                    break;

                case "Approved":
                    $qry->where("user_status", "Approved");
                    break;


                default:
                    $qry->whereIn("user_status", ['InProgress', 'UnderReview', 'ChangeRequested', 'Active', 'Declined', 'Approved']);
                    break;
            }
            $params = DataTableHelper::getParams($request);
            if (empty($params['columnName'])) {
                $params['columnName']   = 'new_onboarding_instructor.updated_at';
            }
            if ($params['columnName'] == 'applicationSubmittedDate') {
                $params['columnName']   = 'new_onboarding_instructor.updated_at';
            }
            if ($params['columnName'] == 'id') {
                $params['columnName']   = 'new_onboarding_instructor.id';
            }
            if ($params['columnName'] == 'applicationStartDate') {
                $params['columnName']   = 'new_onboarding_instructor.created_at';
            }
            if ($params['columnName'] == 'approvedDeclinedDate') {
                $params['columnName']   = 'new_onboarding_instructor.status_updated_at';
            }
            if ($params['columnName'] == 'inpersonRate') {
                $qry->addSelect([
                    'inpersonRate' => InstructorThirdStepOnboardingModel::selectRaw("
                                        CASE
                                            WHEN FIND_IN_SET('hybrid', format) THEN
                                                SUBSTRING_INDEX(compensation, ',', 1)
                                            WHEN FIND_IN_SET('in-person', format) THEN
                                                compensation
                                        END
                                    ")
                        ->whereColumn('user_id', 'new_onboarding_instructor.id')
                        ->limit(1)
                ]);
            }

            if ($params['columnName'] == 'onlineRate') {
                $qry->addSelect([
                    'onlineRate' => InstructorThirdStepOnboardingModel::selectRaw("
                                        CASE
                                            WHEN FIND_IN_SET('hybrid', format) THEN
                                                SUBSTRING_INDEX(compensation, ',', 1)
                                            WHEN FIND_IN_SET('online', format) THEN
                                                compensation
                                            ELSE NULL
                                        END
                                    ")
                        ->whereColumn('user_id', 'new_onboarding_instructor.id')
                        ->limit(1)
                ]);
            }

            if ($params['columnName'] == 'format') {
                $qry->addSelect([
                    'format' => InstructorThirdStepOnboardingModel::selectRaw("
                                        CASE
                                            WHEN FIND_IN_SET('hybrid', format) THEN 'Both (Online & In-person)'
                                            WHEN FIND_IN_SET('online', format) THEN 'Online'
                                            WHEN FIND_IN_SET('in-person', format) THEN 'In-person'
                                            ELSE NULL
                                        END
                                    ")
                        ->whereColumn('user_id', 'new_onboarding_instructor.id')
                        ->limit(1)
                ]);
            }

            if ($params['columnName'] == 'subjects') {
                $qry->addSelect([
                    'subjects' => InstructorSubjectsThirdStepOnboardingModel::select('subjects_v1.subject_code')
                        ->join('subjects_v1', 'onboarding_instructor_subjects.sub_subject', '=', 'subjects_v1.id')
                        ->whereColumn('onboarding_instructor_subjects.user_id', 'new_onboarding_instructor.id')
                        ->limit(1)
                ]);
            }
            $qry->orderBy($params['columnName'], $params['columnSortOrder'] ?? 'desc');

            if ($params['searchValue']) {
                $searchValue = strtolower($params['searchValue']);
                // dd($params['columns'], $searchValue);

                $qry->where(function ($qry) use ($searchValue, $params) {
                    $qry->where(function ($query) use ($searchValue, $params) {
                        // Check if $searchValue is a valid date
                        if (strtotime($searchValue)) {
                            $formattedDate = Carbon::parse($searchValue)->format('Y-m-d');
                            $userIds = OnboardingInstructor::whereDate('created_at', $formattedDate)->pluck('id');
                            $query->whereIn('id', $userIds);
                        }
                    })->orWhere(function ($subQuery) use ($searchValue, $params) {
                        $filteredColumns = array_filter($params['columns'], function ($column) {
                            return isset($column['data']) && $column['data'] === 'subjects';
                        });
                        // dd($filteredColumns);
                        if (!empty($filteredColumns)) {
                            $subjectIds = V1Subject::where('subject_code', 'LIKE', "%{$searchValue}%")
                                ->orWhere('title', 'LIKE', "%{$searchValue}%")
                                ->pluck('id');
                            // dd($subjectIds);
                            if ($subjectIds->isNotEmpty()) {
                                $userIds = InstructorSubjectsThirdStepOnboardingModel::whereIn('sub_subject', $subjectIds)->pluck('user_id');
                                $subQuery->whereIn('id', $userIds);
                            }
                        }
                        // dd($subQuery->get());
                    })->orWhere(function ($subQuery) use ($params) {
                        $filteredColumns = array_filter($params['columns'], function ($column) {
                            return $column['data'] !== 'applicationStartDate' && $column['data'] !== 'applicationSubmittedDate' && $column['data'] !== 'subjects';
                        });

                        if (!empty($filteredColumns)) {
                            $params['columns'] = $filteredColumns; // Update columns
                            DataTableHelper::applySearchFilter($subQuery, $params['searchValue'], $params['columns']);
                        }
                    });
                });

                // $qry->where(function ($que) use ($params) {
                //     DataTableHelper::applySearchFilter($que, $params['searchValue'], $params['columns']);
                // });
            }

            [$count, $result] = DataTableHelper::applyPagination($qry, $params['row'], $params['rowperpage']);

            $data = [];
            $i = 1;
            $res = get_permission(session('Adminnewlogin')['type']);

            foreach ($result as $row) {
                $encryptedId = encrypt($row->id);
                $encryptedStrId = encrypt_str($row->id);

                ///
                $chat = "";
                if (isset($res['manageinstructor'])) :
                    if (array_key_exists('manageinstructor', $res)) :
                        if (in_array('chat', json_decode($res['manageinstructor'], true))) :
                            $chat = $this->generateChatLink($encryptedId);
                        endif;
                    endif;
                endif;

                $checboxButton  = '';
                $action = $this->generateActionButtons($encryptedStrId, $res);

                if (isset($res['manageinstructor'])) :
                    if (array_key_exists('manageinstructor', $res)) :
                        if (in_array('chat', json_decode($res['manageinstructor'], true))) :
                            $checboxButton  = "<input type='checkbox'  value='{$row->id}' form='chatprogram' name='instructor[]'><input type='hidden'  value='{$row->email}' form='chatprogram' id='email{$row->id}'><input type='hidden'  value='{$row->first_name} {$row->last_name}' form='chatprogram' id='to_name{$row->id}'><input type='hidden'  value='{$row->image}' form='chatprogram' id='img{$row->id}'> ";
                        endif;
                    endif;
                endif;

                if ($row->is_sub == 1) {
                    $aval = 'Sub';
                } else {
                    $aval = 'Main/Sub';
                }

                $step3 = InstructorThirdStepOnboardingModel::where("user_id", $row->id)->first();
                $prefrence = !empty($step3) && !empty($step3->i_prefer_to_teach) ? '<a target="_blank" href="' . url('admin/viewonboardinginstructordetails/step3/' . encrypt_str($row->id)) . '">Teaching preferences</a>' : 'null';
                $format = (!empty($step3) && !empty($step3->format) && strpos($step3->format, 'hybrid') !== false) ? "Both (Online & In-person)" : (!empty($step3->format) ? ucfirst($step3->format) : null);
                if (!empty($step3) && !empty($step3->format) && strpos($step3->format, 'hybrid') !== false && !empty($step3->compensation)) {
                    $compensation = explode(',', $step3->compensation);
                    $online = $compensation[0];
                    $inperson = $compensation[1];
                } else {
                    $compensation = (!empty($step3) && !empty($step3->format) && !empty($step3->compensation)) ? $step3->compensation : null;
                    $online = '';
                    $inperson = '';
                    if (!empty($step3->format) && !empty($step3->compensation)) {
                        if (strpos($step3->format, 'online') !== false) {
                            $online = $compensation;
                        } elseif (strpos($step3->format, 'in-person') !== false) {
                            $inperson = $compensation;
                        }
                    }
                }

                $statusButton = '';
                if (isset($res['manageinstructor'])) :
                    if (array_key_exists('manageinstructor', $res)) :
                        if (in_array('update', json_decode($res['manageinstructor'], true))) :
                            $statusButton = $this->generateStatusButton($row->user_status, $row->id);
                        endif;
                    endif;
                endif;

                $verifiedButton = '';
                if (isset($res['manageinstructor'])) :
                    if (array_key_exists('manageinstructor', $res)) :
                        if (in_array('update', json_decode($res['manageinstructor'], true))) :
                            $verifiedButton = $this->generateVerifiedButton($row->email_verify_status, $row->id);
                        endif;
                    endif;
                endif;
                $approvedDeclinedDate = $row->user_status == 'Active' ? Carbon::parse($row->status_updated_at)->format('d-F-Y H:i:s') : (($row->user_status == 'Declined' && !empty($row->rejectInstructor)) ? Carbon::parse($row->rejectInstructor->date)->format('d-F-Y H:i:s') : '');
                $applicationSubmittedDate = !empty($row->submission_date) ? Carbon::parse($row->submission_date)->format('d-F-Y') : '';
                // dd($applicationSubmittedDate);
                $applicationStartDate = Carbon::parse($row->created_at)->format('d-F-Y H:i:s');
                $pendingSteps = [
                    'step-1' => 'Us Work Authorization',
                    'step-3' => 'Education & Experience',
                    'step-4' => 'Your Preferences',
                    'step-5' => 'Profile',
                    'step-6' => 'Assessment: Quiz',
                ];

                $pending = json_decode($row->pending_onboarding_steps);
                $displayedSteps = [];

                foreach ($pendingSteps as $stepKey => $stepValue) {
                    $isStepPending = false;

                    if (!empty($pending)) {
                        foreach ($pending as $pendingStep) {
                            if (strpos($pendingStep, $stepKey) !== false) {
                                $isStepPending = true;
                                break;
                            }
                        }
                    }

                    if ($isStepPending && isset($pendingSteps[$stepKey])) {
                        $displayedSteps[] = $pendingSteps[$stepKey];
                    }
                }
                $commaSeparatedSteps = implode(', ', $displayedSteps);
                // dd($pending,in_array('step-3', $pending ?? []));

                $subjectTitles = [];
                if (!empty($row->step3->subjects)) {
                    foreach ($row->step3->subjects as $subject) {
                        $subSubjectId = $subject->sub_subject;
                        $subjectModel = V1Subject::find($subSubjectId);
                        if ($subjectModel) {
                            $subjectTitles[] = $subjectModel->subject_code . ': ' . $subjectModel->title;
                        }
                    }
                }
                $subjectString = !empty($subjectTitles) ? implode(', ', $subjectTitles) : '';
                $data[] = [
                    "id" => $row->id,
                    "email_verify_status" => $verifiedButton,
                    // "id" =>$row->id,
                    "first_name" => $row->first_name . ' ' . $row->last_name,
                    "email" => $row->email,
                    "onlineRate" => $online,
                    "inpersonRate" => $inperson,
                    "availibility" => '',
                    "approvals" =>  $aval ?? '',
                    "format" => $format ?? '',
                    "state" => $row->state,
                    "city" => $row->city,
                    'subjects' => $subjectString,
                    'inpersonrate' => $row->inpersonrate,
                    'onlinerate' => $row->onlinerate,
                    "status" => $statusButton,
                    "chat" => $chat,
                    "applicationSubmittedDate" => $applicationSubmittedDate,
                    "approvedDeclinedDate" => $approvedDeclinedDate,
                    "applicationStartDate" => $applicationStartDate,
                    'notes' => requestChangeReson($row->id),
                    "action" => $action,
                    "pending_onboarding_steps" => $commaSeparatedSteps,
                    "step_1" => $this->isStepPending($pending, 'step-1')
                        ?  ''
                        : '<svg width="40px" height="40px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#08a206">
                                    <g id="SVGRepo_bgCarrier" stroke-width="0"/>
                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/>
                                    <g id="SVGRepo_iconCarrier"> <path d="M4 12.6111L8.92308 17.5L20 6.5" stroke="#08a206" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </g>
                                    </svg>',
                    "step_3" => $this->isStepPending($pending, 'step-3')
                        ?   ''
                        : '<svg width="40px" height="40px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#08a206">
                                    <g id="SVGRepo_bgCarrier" stroke-width="0"/>
                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/>
                                    <g id="SVGRepo_iconCarrier"> <path d="M4 12.6111L8.92308 17.5L20 6.5" stroke="#08a206" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </g>
                                    </svg>',
                    "step_4" => $this->isStepPending($pending, 'step-4') ? '' : '<svg width="40px" height="40px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#08a206">
                                    <g id="SVGRepo_bgCarrier" stroke-width="0"/>
                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/>
                                    <g id="SVGRepo_iconCarrier"> <path d="M4 12.6111L8.92308 17.5L20 6.5" stroke="#08a206" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </g>
                                    </svg>',
                    "step_5" => $this->isStepPending($pending, 'step-5') ? '' : '<svg width="40px" height="40px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#08a206">
                                    <g id="SVGRepo_bgCarrier" stroke-width="0"/>
                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/>
                                    <g id="SVGRepo_iconCarrier"> <path d="M4 12.6111L8.92308 17.5L20 6.5" stroke="#08a206" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </g>
                                    </svg>',
                    "step_6" => $this->isStepPending($pending, 'step-6') ? '' : '<svg width="40px" height="40px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#08a206">
                                    <g id="SVGRepo_bgCarrier" stroke-width="0"/>
                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/>
                                    <g id="SVGRepo_iconCarrier"> <path d="M4 12.6111L8.92308 17.5L20 6.5" stroke="#08a206" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/> </g>
                                    </svg>',
                ];

                $i++;
            }

            return DataTableHelper::generateResponse($params['draw'], $count, $data);
        }

        return view("admin.new-instructor.instructor_list");
    }

    public function addApplicant()
    {
        $states = StateModel::where(["country_id" => "239"])->get();
        return view("admin.new-instructor.add_applicant", compact('states'));
    }

    public function storeApplicant(Request $request)
    {
        $validator = FacadesValidator::make($request->all(), [
            'first_name' => 'required|string|max:30',
            'last_name' => 'required|string|max:30',
            'email' => 'required|email|max:50|unique:new_onboarding_instructor,email',
            'state' => 'required|array',
            'city' => 'nullable|string|max:50',
            'profilestatusedit' => 'required|in:16,17,20',
            'onlinerate' => 'nullable|numeric|required_if:profilestatusedit,16,20',
            'inpersonrate' => 'nullable|numeric|required_if:profilestatusedit,17,20',
            'is_sub' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => 'Validation failed', 'errors' => $validator->errors()], 422);
        }

        try {
            FacadesDB::beginTransaction();
            // Handle file upload
            // $imagePath = null;
            // if ($request->hasFile('file_data')) {
            //     $file = $request->file('file_data');
            //     $fileName = time() . '_' . $file->getClientOriginalName();
            //     $imagePath = $file->storeAs('instructor_images', $fileName, 'public');
            // }

            // Create the instructor record
            $length = 6;
            $userId = substr(str_shuffle("0123456789"), 0, $length);
            $randPassword = substr(str_shuffle("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"), 0, $length);
            $instructor = new OnboardingInstructor();
            $instructor->user_id = $userId;
            $instructor->first_name = $request->first_name;
            $instructor->last_name = $request->last_name;
            $instructor->email = $request->email;
            $instructor->password = FacadesHash::make($randPassword);
            $instructor->temp_password_changed = 0;
            $instructor->phone_number = $request->phone ?? ''; // Correct field name
            $instructor->state = implode(',', $request->state);
            $instructor->city = $request->city;
            $instructor->is_sub = $request->has('is_sub') ? 1 : 0;
            // $instructor->image = $imagePath;
            $instructor->about = $request->about ?? '';
            $instructor->type = 5; // Based on the manageInstructor method filter
            $instructor->user_status = 'Active';
            $instructor->email_verify_status = 1; // Set as verified since admin is creating
            $instructor->email_verify_time = now();
            $instructor->status = '1'; // Set as active
            $instructor->profile_status = 2;
            $instructor->email_notification = 1;
            $instructor->app_notification = 1;
            $instructor->educator = 'whizara educator';
            $instructor->onlinerate = $request->profilestatusedit == '16' || $request->profilestatusedit == '20' ? $request->onlinerate : null;
            $instructor->inpersonrate = $request->profilestatusedit == '17' || $request->profilestatusedit == '20' ? $request->inpersonrate : null;
            $instructor->submission_date = now();
            $instructor->status_updated_at = now();
            $instructor->created_at = now();
            $instructor->updated_at = now();
            $instructor->save();

            // Create first step record with work authorization
            $firstStep = new InstructorFirstStepOnboardingModel();
            $firstStep->user_id = $instructor->id;
            $firstStep->reside_united_states = 'yes'; // Default value
            $firstStep->state = $request->state[0]; // Use first state as primary
            $firstStep->city = $request->city ?? '';
            $firstStep->zip_code = '00000'; // Default zip code
            $firstStep->i_am_authorized = 'yes'; // Default value
            $firstStep->specify_you_work_authorization = $request->specify ?? '';
            $firstStep->save();

            // Create third step record with rates based on profile status
            $thirdStep = new InstructorThirdStepOnboardingModel();
            $thirdStep->user_id = $instructor->id;

            switch ($request->profilestatusedit) {
                case '16': // Online only
                    $thirdStep->format = 'online';
                    $thirdStep->compensation = $request->onlinerate;
                    break;
                case '17': // In-person only
                    $thirdStep->format = 'in-person';
                    $thirdStep->compensation = $request->inpersonrate;
                    break;
                case '20': // Both
                    $thirdStep->format = 'hybrid';
                    $thirdStep->compensation = $request->onlinerate . ',' . $request->inpersonrate;
                    break;
            }
            $thirdStep->save();

            // Send email to instructor with credentials
            $template = FacadesDB::table("tbl_email_templates")->where("email_template_id", 51)->first();
            if ($template) {
                $body = str_replace(['{{NAME}}', '{{Password}}', '{{Username}}', '{{USER_ID}}'], [$instructor->first_name . ' ' . $instructor->last_name, $randPassword, $instructor->email, $instructor->id], $template->description);
                try {
                    FacadesMail::send('template', ['template' => $body], function ($message) use ($instructor, $template) {
                        $message->to($instructor->email)->subject($template->subject);
                    });
                } catch (Exception $e) {
                    Log::error('Email sending error: ' . $e->getMessage());
                }
            }

            // Commit the transaction
            FacadesDB::commit();
            return response()->json(['success' => true, 'message' => 'Applicant added successfully', 'redirect' => url('admin/k12connections/manage-instructor/ALL')]);
        } catch (ValidationException $e) {
            return response()->json(['success' => false, 'message' => 'Validation failed', 'errors' => $e->errors()], 422);
        } catch (Exception $e) {
            FacadesDB::rollback();
            Log::error('Error creating applicant: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to create applicant. Please try again.'], 500);
        }
    }

    public function updateSubstitue(Request $request)
    {
        try {
            $validated = $request->validate([
                'is_sub' => 'required|in:0,1,2'
            ]);

            FacadesDB::beginTransaction();
            $instructor = UserOnboardingFinalization::updateOrCreate(
                ['user_id' => $request->user_id],
                ['open_to_substitute_opportunity' => $validated['is_sub']]
            );
            FacadesDB::commit();
            return response()->json(['success' => true, 'message' => 'Substitute status updated.']);
        } catch (Exception $e) {
            FacadesDB::rollBack();
            Log::error('Something went wrong: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred'], 500);
        }
    }

    // ********Reset-Applicant-Password********
    public function resetApplicantPassword($user_id)
    {
        $newApplicant = OnboardingInstructor::where("id", $user_id)->first();
        if (!$newApplicant) {
            return redirect()->back();
        }
        return view("admin.new-instructor.resetapplicantpassword", compact("user_id", "newApplicant"));
    }
    // ********Reset-Applicant-Password********

    // ********Reset-Applicant-Password-Functionality********
    public function resetApplicantPasswordFrm(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:new_onboarding_instructor,id',
            'email' => 'required|email|exists:new_onboarding_instructor,email',
            'temp_password' => 'required|string',
            'new_password' => 'required|string|min:8',
            'confirm_password' => 'required|string|same:new_password',
        ]);
        $newApplicant = OnboardingInstructor::where("id", $request->user_id)->first();
        if (!FacadesHash::check($request->temp_password, $newApplicant->password)) {
            return response()->json(['success' => false, 'message' => 'Temporary password is incorrect.']);
        }
        $newApplicant->password = FacadesHash::make($request->new_password);
        $newApplicant->temp_password_changed = 1;
        $newApplicant->save();

        FacadesAuth::guard('instructor')->login($newApplicant);
        $request->session()->put("instructorlogin", ["id" => encrypt($newApplicant->id), "name" => $newApplicant->first_name, "email" => $newApplicant->email, "type" => $newApplicant->type]);

        $template = FacadesDB::table("tbl_email_templates")->where("email_template_id", 52)->first();
        if ($template) {
            $userName = $newApplicant->first_name. ' ' . $newApplicant->last_name;
            $body = str_replace('{{NAME}}', $userName, $template->description);
            try {
                FacadesMail::send('template', ['template' => $body], function ($message) use ($newApplicant, $template) {
                    $message->to($newApplicant->email)->subject($template->subject);
                });
            } catch (Exception $e) {
                Log::error('Email sending error: ' . $e->getMessage());
            }
        }

        return response()->json(['success' => true, 'message' => 'Password updated successfully.' , 'redirect' => route('instructorDashboard')]);
    }
    // ********Reset-Applicant-Password-Functionality********

    public function setInstructorId(Request $request, $id = null)
    {
        $instructorId = $request->input('instructor_id', $id);
        session(['selected_instructor_id' => $instructorId]);
        return redirect()->route('admin.marketplaceviewapplication', ['id' => 'step1']);
    }


    public function sendmsg(Request $request)
    {
        if (!$request->filled('instructor')) {
            return $this->jsonErrorResponse(["message" => "Please select at least one instructor"], false);
        }

        $view = view("admin.new-instructor.modals.sendmsgmodel")->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function sendOnboardingMsgForAdmin(Request $request)
    {
        $from_id = session("Adminnewlogin")["id"];
        $toid = $_POST['id'];
        $message = $_POST['message'];
        $message_type = $_POST['message_type'];
        $count = 0;
        foreach ($toid as $id) {
            $already = LastChatModel::where(['from_id' => $from_id, 'to_id' => $id, 'message' => $message])->first();
            if (!$already) {
                $count++;
                $user = OnboardingInstructor::find($id);
                $email = $user['email'];
                $emailTemplate = EmailTemplate::where('title', 'Message copy')->limit(1)->get();
                $subject = $this->getEmailSubject($message_type);
                $fullName = $user['first_name'] . ' ' . $user['last_name'];
                $emailBody = str_replace(['{{ NAME }}', '{{ message }}'], [$fullName, $message], $emailTemplate[0]->description);
                NotificationHelper::sendEmail($email, $subject, $emailBody);
                LastChatModel::insert(['from_id' => $from_id, 'to_id' => $id, 'message' => $message]);
            }
        }
        if ($count > 0) {
            return response()->json([
                "success" => true,
                "message" => "Message sent successfully",
            ]);
        } else {
            return response()->json([
                "success" => false,
                "message" => "Message already sent",
            ]);
        }
    }

    public function sendRejectOnboardingMsg(Request $request)
    {
        $view = view("admin.new-instructor.modals.rejectmsgModel")->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function sendChangeRequestOnboardingMsg(Request $request)
    {
        $view = view("admin.new-instructor.modals.changeRequestmsgModel")->render();
        return response()->json(['status' => true, 'view' => $view]);
    }

    public function sendActiveOnboardingMsg(Request $request)
    {

        $user = OnboardingInstructor::with(['step1', 'step2', 'step3'])->where('id', $request->userid)->first();
        $subjectIds = $user->step3->subjects->pluck('sub_subject');
        $subjects = V1Subject::with('subjectBudget')->whereIn('id', $subjectIds)->get();
        $case_management = 0;
        $bilingual = 0;
        $sped=0;
        $matchedEducations = empty($user->step2->education)
            ? []
            : collect($user->step2->education)
            ->filter(function ($edu) {
                return stripos($edu->education ?? '', 'special education') !== false;
            })
            ->mapWithKeys(function ($edu) {
                $stateKeys = json_decode($edu->states, true) ?: [];

                $statesWithPrices = BudgetState::whereIn('name', $stateKeys)
                    ->get()
                    ->map(function ($state) {
                        $price = number_format($state->sped_rec_comp, 2); // Format price if needed
                        return [$state->name, $price];
                    })
                    ->values()
                    ->toArray();
                return [$edu->education => $statesWithPrices];
            })
            ->toArray();
        $languages = array_map('trim', explode(',', $user->step3->language_teach_that_i_teach));

        $hasOtherLanguages = collect($languages)->filter(function ($lang) {
            return strtolower($lang) !== 'english';
        })->isNotEmpty();

        $stateData = BudgetState::where('name', $user->step1->state)->first();
        if ($stateData)
        {
            $case_management = $stateData->case_management;
            if($hasOtherLanguages) {
                $bilingual = $stateData->bilingual_inc ?? 0;
            }
            $sped = empty($matchedEducations) ? 0 : $stateData->sped_rec_comp;
        }

        $view = view("admin.new-instructor.modals.activemsgModel", ['user' => $user, 'subjects' => $subjects, "case_management" => $case_management, "bilingual" => $bilingual, "specialEducation" => $matchedEducations, 'sped' => $sped])->render();

        return response()->json(['status' => true, 'view' => $view]);
    }

    public function update_user_status(Request $request)
    {

        $user = OnboardingInstructor::find($request->userid);
        $dataUpdate = [
            'user_status' => $request->status
        ];
        $userEmail = $user->email;
        $adminUser = User::find(3);
        if ($request->status == 'Approved') {
            $request->validate([
                'userid' => 'required|exists:new_onboarding_instructor,id',
                'in_person' => 'required|numeric',
                'bilingual_inc' => 'required|numeric',
                'case_management' => 'required|numeric',
                'budget_lines' => 'required|array',
                'budget_lines.*.code_title' => 'required|string',
                'budget_lines.*.base_pay' => 'required|numeric',
                'budget_lines.*.experience_pay' => 'required|numeric',
                'budget_lines.*.education_pay' => 'required|numeric',
                'budget_lines.*.non_teaching' => 'nullable|numeric',
                'budget_lines.*.special_education' => 'nullable|numeric',
                'budget_lines.*.total' => 'required|numeric'
            ]);
            $template = EmailTemplate::find(35);
            $body = str_replace(['{{NAME}}'], [$user->first_name . ' ' . $user->last_name], $template->description);
            $data = array('template' => $body);
            $subject = $template->subject;


            try {
                DB::beginTransaction();
                // Update or create the main budget approval record
                $approved = InstructorBudgetApprovedModel::updateOrCreate(
                    ['user_id' => $request->userid], // Unique by user
                    [
                        'in_person' => $request->in_person,
                        'bilingual_inc' => $request->bilingual_inc,
                        'case_management' => $request->case_management,
                        'status_updated_at' => now(),
                    ]
                );

                // Then handle each budget line uniquely by subject
                foreach ($request->budget_lines as $line) {
                    $parts = explode(':', $line['code_title']);
                    $subject_code = array_shift($parts);
                    $subject_name = implode(':', $parts);

                    if ($line['selected'] == 'true') {
                        InstructorBudgetLine::updateOrCreate(
                            [
                                'approved_id' => $approved->id,
                                'subject_code' => $subject_code,
                            ],
                            [
                                'subject_title' => $subject_name,
                                'base_pay' => $line['base_pay'],
                                'experience_pay' => $line['experience_pay'],
                                'education_pay' => $line['education_pay'],
                                'non_teaching' => $line['non_teaching'] ?? 0,
                                'special_education' => $line['special_education'] ?? 0,
                                'total' => $line['total'],
                            ]
                        );
                    }
                }

                Mail::send('template', $data, function ($message) use ($userEmail, $subject) {
                    $message->to($userEmail)->subject($subject);
                });
                DB::commit();
            } catch (\Exception  $e) {
                DB::rollback();
                return response()->json([
                    'status' => false,
                    'message' => 'Failed to save instructor budget.',
                    'error' => $e->getMessage()
                ], 500);
            }
        } elseif ($request->status == 'ChangeRequested') {

            $reject = new RequestChangeOnboardingInstructorModel;
            $reject->onboarding_instructor_id = $user->id;
            $reject->reason = $request->reason;
            $reject->date = date('Y-m-d h:i:s');
            $reject->save();

            $template = EmailTemplate::find(37);
            $url = url('k12connections/application');
            $body = str_replace(['{{NAME}}', '{{custom_feedback}}', '{{redirect}}'], [$user->first_name . ' ' . $user->last_name, $request->reason, $url], $template->description);
            // NotificationHelper::sendEmail($user->email, $template->subject, $body);
            $data = array('template' => $body);
            $subject = $template->subject;
            // SendNotificationForInstructorProfileResubmit($user, $adminUser);
            try {

                Mail::send('template', $data, function (
                    $message
                ) use ($userEmail, $subject) {
                    $message->to($userEmail)->subject($subject);
                });
            } catch (\Throwable $th) {
                //throw $th;
            }
        } elseif ($request->status == 'Declined' || $request->status == 'Active') {
            $admin = User::find(3);
            $template = EmailTemplate::find(36);
            $url = url('admin/k12connections/set-instructor-id/' . $user->id);
            $body = str_replace(['{{NAME}}', '{{redirect}}'], [$user->first_name . ' ' . $user->last_name, $url], $template->description);
            $subject = str_replace(['{{NAME}}'], [$user->first_name . ' ' . $user->last_name], $template->subject);
            NotificationHelper::sendEmail($admin->email, $subject, $body);
            if ($request->status == 'Declined') {
                $reject = new RejectOnboardingInstructorModel;
                $reject->onboarding_instructor_id = $user->id;
                $reject->reason = $request->reason;
                $reject->date = date('Y-m-d h:i:s');
                $reject->save();
                // no email trigger
            } elseif ($request->status == 'Active') {
                // $template = EmailTemplate::find(35);
                // $body = str_replace(['{{NAME}}'], [$user->first_name.' '. $user->last_name], $template->description);
                // // NotificationHelper::sendEmail($user->email, $template->subject, $body);
                // $data=array('template'=>$body);
                // $userEmail = $user->email;
                // $subject=$template->subject;
                // // SendNotificationForInstructorProfileApproval($user, $adminUser);

                // try {

                //     Mail::send('template', $data, function (
                //         $message
                //     ) use ($userEmail,$subject) {
                //         $message->to($userEmail)->subject($subject);
                //     });
                // } catch (\Throwable $th) {
                //     //throw $th;
                // }
            }
        } else if ($request->status == 'UnderReview') {
            $template = EmailTemplate::find(45);
            $url = url('k12connections/application');
            $body = str_replace(['{{NAME}}'], [$user->first_name . ' ' . $user->last_name], $template->description);
            // NotificationHelper::sendEmail($user->email, $template->subject, $body);
            $data = array('template' => $body);
            $subject = $template->subject;
            try {

                Mail::send('template', $data, function (
                    $message
                ) use ($userEmail, $subject) {
                    $message->to($userEmail)->subject($subject);
                });
            } catch (\Throwable $th) {
                //throw $th;
            }

            OnboardingInstructor::where('id', $user->id)->update(['educator' => null]);
            OnboardingInstructorMarketplaceContract::where('user_id', $user->id)->delete();
            OnboardingInstructorContract::where('user_id', $user->id)->delete();
        }
        $user->update($dataUpdate);
        return response()->json(['status' => true]);
    }

    public function chat($id)
    {
        $id = decrypt($id);

        $rows = OnboardingInstructor::where("id", $id)->first();

        return view("admin.instructor.chat", compact("rows"));
    }

    public function delete_admin(Request $request)
    {
        $id = decrypt_str($request->id);

        if (isset($id)) {
            $record = OnboardingInstructor::where("id", $id)->first();
            if ($record) {

                $user = $record;

                $res = OnboardingInstructor::where("id", "=", $id)->delete();


                // if($res) {
                //     OnboardingIinstructorGoogleMapAddress::where('instructor_onboarding_id', $id)->delete();

                return response()->json([
                    "success" => true,
                    "message" => "Successfully Deleted",
                ]);
                // } else {
                //     return response()->json([
                //         "success" => false,
                //         "message" => "Something went worng",
                //     ]);
                // }
            } else {
                return response()->json([
                    "success" => false,
                    "message" => "Record not found",
                ]);
            }
        }
    }

    public function viewnewinstructordetails($id, $id2)
    {
        $application_id = decrypt_str($id2);

        $users = OnboardingInstructor::where("id", $application_id)
            ->firstOrFail();

        $user = OnboardingInstructor::with(['step1', 'step2', 'step2.education', 'step2.teching', 'step2.otherExper', 'step3.subjects', 'step5', 'step6', 'step7', 'googleMap'])->find($application_id);

        $user_first_step = InstructorFirstStepOnboardingModel::where("user_id", $application_id)
            ->first();
        $user_second_step = InstructorSecondStepOnboardingModel::with(['teching', 'education', 'otherExper'])
            ->where("user_id", $application_id)
            ->first();
        $user_third_step = InstructorThirdStepOnboardingModel::with(['subjects'])->where("user_id", $application_id)
            ->first();

        $user_fifth_step = InstructorFifthStepOnboardingModel::where("user_id", $application_id)
            ->first();
        $user_sixth_step = InstructorSixthStepOnboardingModel::where("user_id", $application_id)
            ->first();
        $user_seventh_step = InstructorSeventhStepOnboardingModel::where("user_id", $application_id)
            ->first();

        $question = QuestionsModel::orderBy("question_id", "asc")->get();
        $state = StateModel::where(["country_id" => "239"])->get();
        // dd($user,$application_id,$user_first_step,$user_second_step,$user_third_step,$user_fourth_step,$user_fifth_step,$user_sixth_step,$user_seventh_step);

        return view(
            "admin.new-instructor.viewinstructor",
            compact(
                "question",
                "state",
                "user",
                "application_id",
                "user_first_step",
                "user_second_step",
                "user_third_step",
                "user_fifth_step",
                "user_sixth_step",
                "user_seventh_step",
            )
        );
    }

    public function marketplaceviewapplication($id)
    {
        $instructorId = session('selected_instructor_id');
        $application_id = decrypt_str($instructorId);
        $user = OnboardingInstructor::with(['step1', 'step2', 'step3', 'onboardingFinalization'])->where("id", $application_id)->first();

        $educatorData = OnboardingInstructor::where("id", $application_id)->where('user_status', 'Active')->whereNotNull('educator')->whereNull('isDeclineContract')->first();
        if ($educatorData) {
            $marketplaceExists = false;
            $whizaraExists = false;

            $educatorTypes = explode(',', $educatorData->educator);
            if (in_array('marketplace educator', $educatorTypes)) {
                $marketplaceExists = OnboardingInstructorMarketplaceContract::where('user_id', $educatorData->id)->exists();
            }
            if (in_array('whizara educator', $educatorTypes)) {
                $whizaraExists = OnboardingInstructorContract::where('user_id', $educatorData->id)->exists();
            }

            if ($marketplaceExists && $whizaraExists) {
                $educator = 'Marketplace Educator and Whizara Educator';
            } elseif ($marketplaceExists) {
                $educator = 'Marketplace Educator';
            } elseif ($whizaraExists) {
                $educator = 'Whizara Educator';
            } else {
                $educator = 'Educator Not Defined';
            }
        } else {
            $educator = 'Educator Not Defined';
        }

        if (!empty($user)) {
            $data["user_first_step"] = InstructorFirstStepOnboardingModel::where('user_id', $application_id)->first();
            $data["user_third_step"] = InstructorSecondStepOnboardingModel::with(['education', 'teching', 'otherExper', 'references'])
                ->where('user_id', $application_id)
                ->get();
            $data["user_fourth_step"] = InstructorThirdStepOnboardingModel::with(['subjects'])->where("user_id", $application_id)
                ->get();
            $data["profile"] = InstructorFifthStepOnboardingModel::where("user_id", $application_id)->first();
            $data["question"] = QuestionsModel::orderBy("question_id", "asc")->get();
            $data["assessment"] = FreeResponseQuestionsModel::orderBy("id", "asc")->get();
            $data["quiz"] = InstructorSixthStepOnboardingModel::where(["user_id" => $application_id])->first();
            $data["assessments_ans"] = InstructorOnboardingAssessmentModel::where(["user_id" => $application_id])->first();
            $data["whizaraEducator"] = OnboardingInstructorContract::where(["user_id" => $application_id])->first();
            $data["marketplaceEducator"] = OnboardingInstructorMarketplaceContract::where(["user_id" => $application_id])->first();
            $data["additional_certificates"] = DB::table("additional_certificates")->where("instructor_id", $application_id)->get();
            $data["sample_lesson"]=SampleLesson::where("user_id",$application_id)->get();

        //this is for the modal data of checkboxes
        $subjectIds = empty($user->step3) ? [] : $user->step3->subjects->pluck('sub_subject');
        $subjects = empty($user->step3) ? [] : V1Subject::with('subjectBudget')->whereIn('id', $subjectIds)->get();
        $approvedBudget = InstructorBudgetApprovedModel::with('lines')->where('user_id', $application_id)->get();
        $approvedBudgetLines = InstructorBudgetApprovedModel::with('lines')
            ->where('user_id', $application_id)
            ->get()
            ->flatMap(function ($budget) {
                return $budget->lines;
            });
        $case_management = 0;
        $bilingual = 0;
        $sped=0;
        $matchedEducations = empty($user->step2->education)
            ? []
            : collect($user->step2->education)
            ->filter(function ($edu) {
                return stripos($edu->education ?? '', 'special education') !== false;
            })
            ->mapWithKeys(function ($edu) {
                $stateKeys = json_decode($edu->states, true) ?: [];

                $statesWithPrices = BudgetState::whereIn('name', $stateKeys)
                    ->get()
                    ->map(function ($state) {
                        $price = number_format($state->sped_rec_comp, 2); // Format price if needed
                        return [$state->name, $price];
                    })
                    ->values()
                    ->toArray();
                return [$edu->education => $statesWithPrices];
            })
            ->toArray();

        $stateData = empty($user->step1) ? [] : BudgetState::where('name', $user->step1->state)->first();
        if ($stateData)
        {
            $case_management = $stateData->case_management;
            $bilingual = $stateData->bilingual_inc;
            $sped = $stateData->sped_rec_comp;
        }

                // $view = view("admin.new-instructor.modals.activemsgModel", ['user' => $user, 'subjects' => $subjects, "case_management" => $case_management, "bilingual" => $bilingual, "specialEducation" => $matchedEducations, 'sped' => $sped])->render();

            return view("admin.marketplace.viewdetails", compact("data", "application_id", "user", "educator","user","subjects","case_management","bilingual","matchedEducations","sped", 'approvedBudget', 'approvedBudgetLines'));
        } else {
            return redirect('/admin-dashboard');
        }
    }

    public function exportApplicants(Request $request)
    {
        try {
            ob_end_clean();
            ob_start();
            $fileName = 'Applicants' . time() . '.xlsx';
            return Excel::download(new ExportMarketplaceApplicants($request), $fileName, \Maatwebsite\Excel\Excel::XLSX);
        } catch (\Exception $e) {
            // echo $e->getMessage();die;
            return redirect()->back();
        }
    }

    // ********Generate-Platform-Action-Buttons********
    private function generatePlatformActionButtons($encryptedStrId, $res)
    {
        $viewRoute = url('admin/k12connections/view-platform-schools/' . $encryptedStrId);
        $editRoute = url('admin/k12connections/edit-platform-schools/' . $encryptedStrId);
        $deleteButton = url('admin/k12connections/delete-platform-schools/' . $encryptedStrId);
        $actionUrl = "javascript:void(0);";
        $editButton = $deleteButton = '';

        if (isset($res['managemarketplace'])) {
            $permissions = json_decode($res['managemarketplace'], true);
            if (in_array('platform schools', $permissions)) {
                $viewButton = "<a href='{$viewRoute}'><button type='button' title='View' class='btn btn-xs btn-outline-secondary mx-1'><i class='fa fa-eye'></i></button></a>";
                $editButton = "<a href='{$editRoute}'><button type='button' title='Edit' class='btn btn-xs btn-outline-primary mx-1'><i class='fa fa-pencil'></i></button></a>";
            }
            if (in_array('delete platform schools', $permissions)) {
                $deleteButton = "<button type='button' title='Delete' data-id='{$encryptedStrId}' class='btn btn-xs btn-outline-danger mx-1 delete-platform-school'><i class='fa fa-trash'></i></button>";
            }
        }
        return "<div class='d-flex justify-content-center'>{$viewButton}{$editButton}{$deleteButton}</div>";
    }
    // ********Generate-Platform-Action-Buttons********

    // ********List-Platform-Schools********
    public function managePlatformSchools(Request $request)
    {
        try {
            $adminType = session('Adminnewlogin')['type'] ?? null;
            if (!get_childpermission(get_permission($adminType), 'managemarketplace', 'platform schools')) {
                return redirect("/no-permission");
            }

            if ($request->ajax()) {
                $params = DataTableHelper::getParams($request);
                $columnName = $params['columnName'] ?? 'schools.id';
                $columnSortOrder = $params['columnSortOrder'] ?? 'desc';

                // Base query for platform schools
                $query = Schools::where('customer_type', 'platform')->orderBy($columnName, $columnSortOrder);

                // Apply search filter
                $query->where(function ($q) use ($params) {
                    DataTableHelper::applySearchFilter($q, $params['searchValue'], $params['columns']);
                });

                // Get paginated results
                [$count, $results] = DataTableHelper::applyPagination($query, $params['row'], $params['rowperpage']);
                $permissions = get_permission($adminType);
                $manageSchoolPermissions = json_decode($permissions['manageschool'] ?? '[]', true);
                $data = [];
                foreach ($results as $row) {
                    $encryptedId = encrypt($row->id);
                    $encryptedStrId = encrypt_str($row->id);
                    $chat = (in_array('Manage Program', $manageSchoolPermissions)) ? $this->generateChatLink($encryptedId) : '';
                    $data[] = [
                        "id" => $row->id,
                        "school_name" => $row->school_name,
                        "email" => $row->email,
                        "organization_type" => $row->organization_type,
                        // "organizationname" => $row->organizationname ?? '',
                        // "status" => $this->generateStatusButton($row->status, $row->id),
                        "created_at" => getAdminTimestamp($row->created_at),
                        "action" => $this->generatePlatformActionButtons($encryptedStrId, $permissions),
                    ];
                }
                return DataTableHelper::generateResponse($params['draw'], $count, $data);
            }
            return view("admin.marketplace.schools.manageplatformschools", ['user_list' => []]);
        } catch (Exception $e) {
            report($e);
            return redirect()->back()->with('error', 'Something went wrong.');
        }
    }
    // ********List-Platform-Schools********

    // ********View-Platform-Schools********
    public function viewPlatformSchools($id)
    {
        $user_id = decrypt_str($id);
        $school_contact_info = FacadesDB::table("school_users")->where("school_id", $user_id)->get();
        $user_list = Schools::where("id", $user_id)->first();
        $schoolRoles = SchoolRole::with('actions')->get();
        return view("admin.marketplace.schools.viewplatformschools", compact("user_list", "user_id", 'school_contact_info', 'schoolRoles'));
    }
    // ********View-Platform-Schools********

    // ********Add-Platform-Schools********
    public function addPlatformSchools()
    {
        try{
            $district = District::where("status", "1")->get();
            $cbo = FacadesDB::table("tbl_cbo")->where("status", "1")->get();
            $gradeLavel = FacadesDB::table("tbl_classes")->where("status", "1")->get();
            $schoolRoles = SchoolRole::with('actions')->get();
            return view("admin.marketplace.schools.addplatformschools", compact("district", "cbo", "gradeLavel", 'schoolRoles'));
        } catch (\Exception $e) {
            return redirect()->back();
        }
    }
    // ********Add-Platform-Schools********

    // ********Save-Platform-Schools********
    public function savePlatformSchools(Request $request)
    {
        try {
            $validator = FacadesValidator::make($request->all(), [
                'email' => 'required|email',
                'full_name' => 'required|string|max:255',
                'profile_upload' => 'nullable|image|max:2048',
                'grade' => 'required|array',
                'job_title' => 'required|array',
                'role_id' => 'required|array',
                'cemail' => 'required|array',
                'first_name' => 'required|array',
                'last_name' => 'required|array',
                'phone' => 'required|array',
            ]);
            if ($validator->fails()) {
                return response()->json(["success" => false, "message" => $validator->errors()->first()]);
            }
            if (Users::where("email", $request->email)->exists()) {
                return response()->json(["success" => false, "message" => "Email already exists"]);
            }

            FacadesDB::beginTransaction();
            $filename = "";
            if ($request->hasFile("profile_upload")) {
                $image = $request->file("profile_upload");
                $filename = 'uploads/school/' . uniqid() . '_' . $image->getClientOriginalName();
                uploads3image($filename, $image);
            }

            $userId = substr(str_shuffle("0123456789"), 0, 6);

            $data = [
                "school_name" => $request->full_name ?? '',
                "user_id" => $userId,
                "email" => $request->email,
                "phone_number" => $request->school_phone,
                "school_rating" => $request->school_rating,
                "organization_type" => $request->organizationtype,
                "nces_id" => $request->nces_cource_code,
                "enrollment_count" => $request->enrollment,
                "grade_levels_id" => implode(",", $request->grade),
                "customer_type" => "platform",
                "website_url" => $request->website,
                "state" => $request->state,
                "city" => $request->city,
                "country" => $request->country,
                "zipcode" => $request->zipcode,
                "address" => $request->address,
                "locale" => $request->locale,
                "timezone" => $request->timezone,
                "status" => 'active',
                "image" => $filename,
                "created_at" => now(),
                "updated_at" => now(),
            ];
            $schoolId = Schools::insertGetId($data);

            $template = FacadesDB::table("tbl_email_templates")->where("email_template_id", 46)->first();
            if ($template && $schoolId) {
                foreach ($request->cemail as $index => $contactEmail) {
                    $randPassword = substr(str_shuffle("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"), 0, 8);
                    $contactName = trim(($request->first_name[$index] ?? '') . ' ' . ($request->last_name[$index] ?? ''));

                    $contact = [
                        "job_title"   => $request->job_title[$index],
                        "role_id"     => $request->role_id[$index],
                        "email"       => $contactEmail,
                        "first_name"  => $request->first_name[$index],
                        "last_name"   => $request->last_name[$index],
                        "phone_number"=> $request->phone[$index],
                        "password"    => FacadesHash::make($randPassword),
                        "school_id"   => $schoolId,
                    ];
                    $schoolUser = SchoolUser::create($contact);

                    // $body = str_replace(
                    //     ['{{NAME}}', '{{Password}}', '{{Username}}', '{{USER_ID}}'],
                    //     [$contactName, $randPassword, $contactEmail, $schoolUser->id], $template->description
                    // );
                    // try {
                    //     FacadesMail::send('template', ['template' => $body], function ($message) use ($contactEmail, $template) {
                    //         $message->to($contactEmail)->subject($template->subject);
                    //     });
                    // } catch (Exception $e) {
                    //     Log::error('Email sending error: ' . $e->getMessage());
                    // }
                }
            }
            FacadesDB::commit();
            return response()->json(["success" => true, "message" => "School successfully created", "redirect" => url("/admin/k12connections/manage-platform-schools")]);
        } catch (Exception $e) {
            FacadesDB::rollBack();
            Log::error('Platform School Creation Failed', ['error' => $e->getMessage()]);
            return response()->json(["success" => false, "message" => "Something went wrong. Please try again later."]);
        }
    }
    // ********Save-Platform-Schools********

    // ********Edit-Platform-Schools********
    public function editPlatformSchools($id)
    {
        $user_id1 = decrypt_str($id);
        $user_id = $id;
        $where = ["id" => $user_id1];
        $user_list = Schools::get_all_client_record($where);
        $schoolRoles = SchoolRole::with('actions')->get();
        $coreActions = CoreAction::all();
        $schoolUserAction = SchoolUserAction::with(['user', 'action'])->where('user_id', $user_id1)->get();
        $district = District::where("status", "1")->get();
        $cbo = FacadesDB::table("tbl_cbo")->where("status", "1")->get();
        $school_contact_info = SchoolUser::with('actions')->where('school_id', $user_id1)->get();
        $gradeLavel = FacadesDB::table("tbl_classes")->where("status", "1")->get();
        return view("admin.marketplace.schools.editplatformschool", compact("user_list", "district", "user_id", "cbo", "gradeLavel", "school_contact_info", 'schoolRoles', 'coreActions', 'schoolUserAction'));
    }
    // ********Edit-Platform-Schools********

    // ********Update-Platform-Schools********
    public function updatePlatformSchools(Request $request)
    {
        try {
            $validator = FacadesValidator::make($request->all(), [
                'user_id' => 'required|exists:schools,id',
                'email' => 'required|email',
                'full_name' => 'required|string|max:255',
                'profile_upload' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'job_title' => 'required|array',
                'role_id' => 'required|array',
                'cemail' => 'required|array',
                'first_name' => 'required|array',
                'last_name' => 'required|array',
                'phone' => 'required|array',
            ]);
            if ($validator->fails()) {
                return response()->json(['success' => false, 'message' => $validator->errors()->first()]);
            }

            $id = $request->user_id;
            FacadesDB::beginTransaction();
            $data = [
                'school_name'       => $request->full_name,
                'email'             => $request->email,
                'phone_number'      => $request->school_phone,
                'organization_type' => $request->organizationtype,
                'school_rating'     => $request->school_rating,
                'enrollment_count'  => $request->enrollment,
                'state'             => $request->state,
                'city'              => $request->city,
                'country'           => $request->country,
                'zipcode'           => $request->zipcode,
                'address'           => $request->address,
                'locale'            => $request->locale,
                'timezone'          => $request->timezone,
                'website_url'       => $request->website,
                'status'            => '1',
                'updated_at'        => now(),
            ];

            if (!empty($request->gradelevel)) {
                $data['grade_levels_id'] = implode(',', $request->gradelevel);
            }
            if ($request->hasFile('profile_upload')) {
                $image = $request->file('profile_upload');
                $filename = 'uploads/school/' . uniqid() . '_' . $image->getClientOriginalName();
                uploads3image($filename, $image);
                $data['image'] = $filename;
            }

            $updated = Schools::where('id', $id)->update($data);
            if ($updated) {
                if ($request->has('job_title')) {
                    foreach ($request->job_title as $index => $title) {
                        if (empty($title) && empty($request->cemail[$index]) && empty($request->first_name[$index]) && empty($request->last_name[$index]) && empty($request->phone[$index])) {
                            continue;
                        }

                        $contactData = [
                            'job_title'    => $request->job_title[$index] ?? '',
                            'role_id'      => $request->role_id[$index] ?? '',
                            'first_name'   => $request->first_name[$index] ?? '',
                            'last_name'    => $request->last_name[$index] ?? '',
                            'phone_number' => $request->phone[$index] ?? '',
                            'school_id'    => $id,
                        ];

                        $schoolUser = SchoolUser::updateOrCreate(
                            ['school_id' => $id, 'email' => $request->cemail[$index]],
                            $contactData
                        );

                        if ($request->has('core_action') && isset($request->core_action[$schoolUser->id])) {
                            $selectedActions = $request->core_action[$schoolUser->id] ?? [];
                            $schoolUser->actions()->sync($selectedActions);
                        } else {
                            $schoolUser->actions()->sync([]);
                        }
                    }
                }
                FacadesDB::commit();
                return response()->json(['success' => true, 'message' => 'Details successfully updated', 'redirect' => url('/admin/k12connections/manage-platform-schools')]);
            }
            FacadesDB::rollBack();
            return response()->json(['success' => false, 'message' => 'Something went wrong.']);
        } catch (Exception $e) {
            FacadesDB::rollBack();
            Log::error('Platform School Update Failed', ['error' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => 'Something went wrong. Please try again later.']);
        }
    }
    // ********Update-Platform-Schools********

    // ********Send-Credentials-Platform-Schools********
    public function sendCredentialsPlatformSchools(Request $request)
    {
        try {
            $schoolUser = SchoolUser::find($request->id);
            if (!$schoolUser) {
                return response()->json(["success" => false, "message" => "User not found"]);
            }

            $template = FacadesDB::table("tbl_email_templates")->where("email_template_id", 46)->first();
            if ($template) {
                $randPassword = substr(str_shuffle("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"), 0, 8);
                $schoolUser->password = FacadesHash::make($randPassword);
                $schoolUser->save();

                $body = str_replace(['{{NAME}}', '{{Password}}', '{{Username}}', '{{USER_ID}}'], [$schoolUser->first_name . ' ' . $schoolUser->last_name, $randPassword, $schoolUser->email, $schoolUser->id], $template->description);
                try {
                    FacadesMail::send('template', ['template' => $body], function ($message) use ($schoolUser, $template) {
                        $message->to($schoolUser->email)->subject($template->subject);
                    });
                } catch (Exception $e) {
                    Log::error('Email sending error: ' . $e->getMessage());
                }
            }
            return response()->json(["success" => true, "message" => "Credentials sent successfully"]);
        } catch (Exception $e) {
            Log::error('Credentials Sending Failed', ['error' => $e->getMessage()]);
            return response()->json(["success" => false, "message" => "Something went wrong. Please try again later."]);
        }
    }
    // ********Send-Credentials-Platform-Schools********

    // ********Update-School-User-Roles********
    public function updateSchoolUserRoles(Request $request)
    {
        try {
            $validator = FacadesValidator::make($request->all(), [
                'user_id' => 'required|exists:school_users,id',
                'role_id' => 'required|exists:school_roles,id',
            ]);
            if ($validator->fails()) {
                return response()->json(['success' => false, 'message' => $validator->errors()->first()]);
            }

            $userId = $request->user_id;
            $roleId = $request->role_id;
            if ($roleId != 1) {
                SchoolUserAction::where('user_id', $userId)->delete();
            }
            SchoolUser::where('id', $userId)->update(['role_id' => $roleId]);
            return response()->json(['success' => true, 'message' => 'Role successfully updated']);
        } catch (Exception $e) {
            Log::error('School User Role Update Failed', ['error' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => 'Something went wrong. Please try again later.']);
        }
    }
    // ********Update-School-User-Roles********

    // ********Delete-Platform-Schools********
    public function deletePlatformSchools(Request $request)
    {
        $id = decrypt_str($request->id);
        if (!$id) {
            return response()->json(["success" => false, "message" => "Invalid ID"]);
        }

        $record = Schools::find($id);
        if (!$record) {
            return response()->json(["success" => false, "message" => "Record not found"]);
        }

        try {
            SchoolUser::where("school_id", $id)->delete();
            $record->delete();
            return response()->json(["success" => true, "message" => "Successfully Deleted"]);
        } catch (Exception $e) {
            Log::error('Platform School Deletion Failed', ['error' => $e->getMessage()]);
            return response()->json(["success" => false, "message" => "Something went wrong", "error"   => $e->getMessage()]);
        }
    }
    // ********Delete-Platform-Schools********

    // ********List-School-Roles********
    public function listSchoolRoles(Request $request)
    {
        if ($request->ajax()) {
            $schoolRoles = SchoolRole::with('actions')->get();
            $formattedRoles = $schoolRoles->map(function ($role) {
                return [
                    'name' => $role->name,
                    'description' => $role->description,
                    'is_system_role' => $role->is_system_role ? '<span class="badge bg-success">Active</span>' : '<span class="badge bg-danger">Inactive</span>',
                    'actions' => '<span class="badge bg-secondary"><a href="' . route('admin.manage-school-roles', $role->id) . '"><i class="fas fa-tools text-white" title="Under Construction"></i></a></span>' // icon only
                ];
            });
            return response()->json(['success' => true, 'roles' => $formattedRoles]);
        }
        return view('admin.marketplace.school-roles.school-roles-list');
    }
    // ********List-School-Roles********

    // ********Manage-Role******

    public function manageSchoolRoles($id)
    {
        $role = SchoolRole::with('actions')->findOrFail($id);
        $allActions = CoreAction::all();

        return view('admin.marketplace.school-roles.manage-school-role', compact('role', 'allActions'));
    }

    // ********Manage-Role******

    // ********Update-Role******

    public function updateSchoolRoles(Request $request, $id)
    {
        $role = SchoolRole::findOrFail($id);

        // Update role details (optional)
        $role->name = $request->input('name');
        $role->description = $request->input('description');
        $role->save();

        // Sync actions (permissions)
        $role->actions()->sync($request->input('actions', []));
        return redirect()->route('admin.list-school-roles')->with('success', 'Role updated successfully!');
    }

    // ********Update-Role******


    // ********Get-Timezone********
    public function getTimezone(Request $request)
    {
        $zipcode = $request->zipcode;
        $apiKey = env('MAP_KEY');

        // Step 1: Get lat/lng using helper
        $components = location($zipcode); // Your existing helper

        $lat = null;
        $lng = null;

        // Find lat/lng from geometry in Google response
        $url = "https://maps.googleapis.com/maps/api/geocode/json?address=" . urlencode($zipcode) . "&key=" . $apiKey;
        $response = Http::get($url);
        $geoData = $response->json();

        if (!isset($geoData['results'][0]['geometry']['location'])) {
            return response()->json(['error' => 'Invalid zipcode'], 400);
        }

        $lat = $geoData['results'][0]['geometry']['location']['lat'];
        $lng = $geoData['results'][0]['geometry']['location']['lng'];

        // Step 2: Get timezone using Time Zone API
        $timestamp = time();
        $timezoneUrl = "https://maps.googleapis.com/maps/api/timezone/json?location=$lat,$lng&timestamp=$timestamp&key=$apiKey";
        $tzResponse = Http::get($timezoneUrl);
        $tzData = $tzResponse->json();

        if ($tzData['status'] === 'OK') {
            return response()->json(['timezone' => $tzData['timeZoneId']]);
        } else {
            return response()->json(['error' => 'Failed to fetch timezone'], 400);
        }
    }
    // ********Get-Timezone********

    // ********Manage-Budget-Schools********
    public function manageBudgetSchools()
    {
        $subjectAreas = V1SubjectArea::all();
        $budget = SchoolBudget::first();
        return view("admin.marketplace.schools.managebudgetschools", compact('subjectAreas', 'budget'));
    }
    // ********Manage-Budget-Schools********
    // ********Update-Budget-Schools********
    public function updateBudgetSchools(Request $request)
    {
        $validated = $request->validate([
            'in_person'        => 'nullable|numeric|min:0',
            'case_management'  => 'nullable|numeric|min:0',
            'bilingual_inc'    => 'nullable|numeric|min:0',
            'sped_rec_comp'    => 'nullable|numeric|min:0',
        ]);

        $budget = SchoolBudget::first();

        $budget->update($validated);

        $subjectAreas = SubjectArea::all();

        return view("admin.marketplace.schools.managebudgetschools", compact('subjectAreas', 'budget'))
            ->with('success', 'School budget updated successfully!');
    }
    // ********update-Budget-Schools********
    // ********Update-Budget-Schools********

    public function schoolBudgetbySubjectArea($subjectAreaId)
    {
        $budgets = V1Subject::with(['schoolSubjectBudget'])
            ->where('subject_area_id', $subjectAreaId)
            ->get();
        return response()->json($budgets);
    }
    // ********update-Budget-Schools********
}
