@extends('admin.layouts.master')

@section('title') View Platform School | Whizara @endsection

@section('content')
<?php $res=get_permission(session('Adminnewlogin')['type']); ?>
<!-- MAIN SECTION START -->
<main class="content">
    <div class="container-fluid p-0">
        <!-- BREADCRUMB START -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                @if(isset($res['dashboard']))
                    @if(array_key_exists('dashboard',$res))
                        @if(in_array('add',json_decode($res['dashboard'] ,true)))
                            <li class="breadcrumb-item"><a href="{{url('admin-dashboard')}}" class="text-primary">{{ __('messages.dashboard') }}</a></li>
                        @endif
                    @endif
                @endif
                @if(isset($res['manageschool']))
                    @if(array_key_exists('manageschool',$res))
                        @if(in_array('view',json_decode($res['manageschool'] ,true)))
                            <li class="breadcrumb-item active float-right" aria-current="page"><a href="{{url('admin/k12connections/manage-platform-schools')}}"> List Platform School</a></li>
                        @endif
                    @endif
                @endif
                <li class="breadcrumb-item active" aria-current="page">View Platform School</li>
            </ol>
        </nav>
        <!-- BREADCRUMB END -->

        <!-- EDIT PROFILE SECTION START -->
        <div class="row justify-content-center">
            <div class="col-lg-2 col-md-3 col-6">
                <div class="change_profile_img mx-auto mb-4">
                    <div class="avatar-preview">
                        @if($user_list->image)
                            <div id="imagePreview" style="background-image: url({{generateSignedUrl($user_list->image)}});"></div>
                        @else
                            <div id="imagePreview" style="background-image: url({{default_user_placeholder()}})"></div>
                        @endif
                    </div>
                </div>

                {{-- <div class="mb-4">
                    @if(isset($res['manageschool']))
                        @if(array_key_exists('manageschool',$res))
                            @if(in_array('update',json_decode($res['manageschool'] ,true)))
                                <a href="javascript:void(0)"  class="btn btn-primary btn-block sendcred">Send Credentials</a>
                                <a href="{{url('change-school-password/'.encrypt_str($user_id))}}" class="btn btn-primary btn-block">Reset Password</a>
                            @endif
                        @endif
                    @endif
                </div> --}}
            </div>

            <div class="col-lg-10 col-md-9">
                <div class="row">
                    <div class="col-lg-12 col-md-9">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="card">
                                    <div class="card-header border-bottom"><h5 class="mb-0">Personal Details</h5></div>
                                    <input type="hidden" value="{{$user_id}}" id="useridc" name="useridc">
                                    <div class="card-body p-0">
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">School Name :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0">{{$user_list->school_name}}</h6>
                                            </li>

                                            {{-- <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">Customer Type :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0">{{$user_list->cust_type}}</h6>
                                            </li>

                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">CBO :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0">{{$user_list->cname}}</h6>
                                            </li> --}}

                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">Grade Level :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0">@if($user_list->grade_levels_id) {{rtrim(gradeLevel($user_list->grade_levels_id), ",")}} @endif </h6>
                                            </li>

                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">Organization Type :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0">{{$user_list->organization_type}}</h6>
                                            </li>

                                            {{-- <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">Organization Name :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0">{{$user_list->organizationname}}</h6>
                                            </li>

                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">District :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0">{{ getDistrictname($user_list->district) }}</h6>
                                            </li> --}}

                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">Website :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0">{{$user_list->website_url}}</h6>
                                            </li>

                                            @if(isset($user_list->danme))
                                                <li class="list-group-item d-flex">
                                                    <span class="col-lg-4 col-md-3 col-4 pl-0">District :</span>
                                                    <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0">{{$user_list->danme}}</h6>
                                                </li>
                                            @endif

                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">Email :</span>
                                                <a class="col-lg-8 col-md-9 col-8 pr-0 mb-0" href="mailto:{{$user_list->email}}">{{$user_list->email}}</a>
                                            </li>

                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">Address :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0">{{$user_list->address}} </h6>
                                            </li>

                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">State :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0">{{$user_list->state}} </h6>
                                            </li>

                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">City :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0">{{$user_list->city}}</h6>
                                            </li>

                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">Zipcode :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0">{{$user_list->zipcode}}</h6>
                                            </li>

                                            <li class="list-group-item d-flex">
                                                <span class="col-lg-4 col-md-3 col-4 pl-0">Note :</span>
                                                <h6 class="col-lg-8 col-md-9 col-8 pr-0 mb-0">{{$user_list->about}}</h6>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- EDIT PROFILE SECTION END -->

        {{-- SCHOOL USER ACTION SECTION START --}}
        <ul class="nav nav-tabs" id="actionTabs" role="tablist">
            <li class="nav-item"><a class="nav-link active" id="user-action-tab" data-toggle="tab" href="#user-action" role="tab" aria-controls="user-action" aria-selected="true">User Action</a></li>
            <li class="nav-item"><a class="nav-link" id="user-management-tab" data-toggle="tab" href="#user-management" role="tab" aria-controls="user-management" aria-selected="false">User Management</a></li>
        </ul>
        <div class="tab-content mt-3" id="actionTabsContent">
            <div class="tab-pane fade show active" id="user-action" role="tabpanel" aria-labelledby="user-action-tab">
                @include('admin.marketplace.schools.tabs.user-action')
            </div>
            <div class="tab-pane fade" id="user-management" role="tabpanel" aria-labelledby="user-management-tab">
                @include('admin.marketplace.schools.tabs.user-management')
            </div>
        </div>
        {{-- SCHOOL USER ACTION SECTION START --}}

    </div>
</main>
<!-- MAIN SECTION END -->
@endsection