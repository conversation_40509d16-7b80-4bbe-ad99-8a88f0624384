<?php

namespace App\Models\V1;

use App\Schools;
use Illuminate\Database\Eloquent\Model;

class SchoolRole extends Model
{
    protected $table = 'school_roles';

    protected $fillable = [
        'name',
        'description',
        'is_system_role'
    ];

    public function actions()
    {
        return $this->belongsToMany(CoreAction::class, 'school_role_actions', 'role_id', 'action_id');
    }
}
