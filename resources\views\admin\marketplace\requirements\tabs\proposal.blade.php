<!-- Invite <PERSON> -->
<style>
    .truncate-text {
        display: inline-block;
        max-width: 300px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: middle;
        cursor: pointer;
    }
</style>
<div class="d-flex justify-content-end gap-2 align-items-center mb-3">
    <button type="button" class="mx-3 btn btn-primary"
        data-toggle="modal"
        data-target="#mainInstructorInvitePopup"
        id="send-invite"
        onclick="inviteMainInstructor('{{ route('admin.marketplace-requirementsInvite') }}', '{{ encrypt_str($data->id) }}')">
        Main Instructor Invite
    </button>
    <button type="button" class="btn btn-primary"
        data-toggle="modal"
        data-target="#standByInvitePopup"
        id="send-invite"
        onclick="inviteStandbyInstructor('{{ route('admin.marketplace-requirementsInvite') }}', '{{ encrypt_str($data->id) }}')">
        Standby Instructor Invite
    </button>
</div>

{{-- Proposal History Table --}}
<div class="card">
    <div class="card-header">
        Proposal History
    </div>
    <div class="card-body p-0">
        @if($data->reviewApplicants->count() > 0)
            <div class="table-responsive">
                <table class="table table-striped table-bordered mb-0">
                    <thead class="thead-dark">
                        <tr>
                            <th>#</th>
                            <th>Educator Name</th>
                            <th>Educator Email</th>
                            <th>Requirement Start Date</th>
                            <th>Invite ID</th>
                            <th>Offer Description</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($data->reviewApplicants as $index => $proposal)
                            @php
                                if ($proposal->status === 'accepted') {
                                    $badgeClass = 'bg-success';
                                } elseif ($proposal->status === 'rejected') {
                                    $badgeClass = 'bg-danger';
                                } elseif ($proposal->status === 'expired') {
                                    $badgeClass = 'bg-secondary';
                                } elseif ($proposal->status === 'pending') {
                                    $badgeClass = 'bg-warning';
                                } else {
                                    $badgeClass = 'bg-info';
                                }
                            @endphp
                            <tr>
                                <td>{{ $index + 1 }}</td>
                                <td>{{ $proposal->user->first_name }} {{ $proposal->user->last_name }}</td>
                                <td>{{ $proposal->user->email }}</td>
                                <td>{{ $proposal->requirement->start_date }}</td>
                                <td>{{ $proposal->id }}</td>
                                <td class="truncate-text" title="{{ $proposal->offer_description }}">{{ $proposal->offer_description }}</td>
                                <td><span class="badge badge-{{ $badgeClass }}">{{ ucfirst(strtolower($proposal->status ?? 'Pending')) }}</span></td>
                                <td>
                                    <button class="btn btn-sm btn-primary" @if($proposal->status != 'pending') disabled @endif onclick="withdrawInvite('{{ route('admin.marketplace-updateAppliedRequestStatus') }}', '{{ $proposal->id }}')">Withdraw</button>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <p class="text-muted m-3">No proposals sent yet.</p>
        @endif
    </div>
</div>


<!-- Modal (only for sending invites) -->
<div class="modal" id="mainInstructorInvitePopup" tabindex="-1" aria-labelledby="mainInstructorInvitePopup" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <!-- Content loaded by AJAX -->
        </div>
    </div>
</div>

<!-- Modal (only for sending invites) -->
<div class="modal" id="standByInvitePopup" tabindex="-1" aria-labelledby="standByInvitePopup" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <!-- Content loaded by AJAX -->
        </div>
    </div>
</div>


@section('scripts')


<script>

    async function inviteMainInstructor(url, encryptedId) {
        $('#mainInstructorInvitePopup .modal-content').html('<span class="loading p-5">Loading...</span>');
        $('#standByInvitePopup .modal-content').html('<span class="loading p-5">Loading...</span>');
        try {
            const response = await $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: url,
                method: "GET",
                data: {
                    id: encryptedId,
                    invitation_type: 'main_instructor'
                }
            });

            if (response) {
                $('#mainInstructorInvitePopup .modal-content').html(response);
            } else {
                alertify.error(response.message);
            }
        } catch (error) {
            alertify.error(error.responseJSON.message);
        }
    }

    async function inviteStandbyInstructor(url, encryptedId) {
        $('#mainInstructorInvitePopup .modal-content').html('<span class="loading p-5">Loading...</span>');
        $('#standByInvitePopup .modal-content').html('<span class="loading p-5">Loading...</span>');
        try {
            const response = await $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: url,
                method: "GET",
                data: {
                    id: encryptedId,
                    invitation_type: 'stand_by'
                }
            });

            if (response) {
                $('#standByInvitePopup .modal-content').html(response);
            } else {
                alertify.error(response.message);
            }
        } catch (error) {
            alertify.error(error.responseJSON.message);
        }
    }

    async function withdrawInvite(url, id) {
        try {
            const response = await $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: url,
                method: "POST",
                data: {
                    invite_id: id,
                    status: 'withdraw'
                }
            });

            if (response) {
                alertify.success(response.message);
                $(`[data-inviteId="${id}"data-field="status"]`).html('Withdrawn');
            } else {
                alertify.error(response.message);
            }
        } catch (error) {
            alertify.error(error.responseJSON.message);
        }
    }

</script>

@endsection