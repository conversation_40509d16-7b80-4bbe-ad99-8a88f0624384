[2025-09-01 00:09:29] local.ERROR: Platform School Update Failed {"error":"SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`whizara2_latest`.`school_users`, CONSTRAINT `school_users_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `school_roles` (`id`) ON DELETE SET NULL) (SQL: insert into `school_users` (`school_id`, `email`, `job_title`, `role_id`, `first_name`, `last_name`, `phone_number`, `updated_at`, `created_at`) values (1, <EMAIL>, Manager, , Fruit, Punch, 9988556622, 2025-09-01 00:09:29, 2025-09-01 00:09:29))"} 
[2025-09-01 00:10:59] local.ERROR: Email sending error: Connection could not be established with host groceryapp.phpdev.co.in :stream_socket_client(): php_network_getaddresses: getaddrinfo failed: No such host is known.   
[2025-09-01 00:33:01] local.ERROR: HTTP Error: 404 {"exception":"RuntimeException","url":"http://127.0.0.1:8000/api/1/review-applicants","status":404} 
[2025-09-01 00:34:33] local.ERROR: HTTP Error: 500 {"exception":"RuntimeException","url":"http://127.0.0.1:8000/api/1/review-applicants","status":500} 
