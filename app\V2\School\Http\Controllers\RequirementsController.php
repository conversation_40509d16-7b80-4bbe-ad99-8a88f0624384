<?php

namespace App\V2\School\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\PlatformSchoolRequirements;
use App\V2\Core\Helpers\ApiResponse;
use App\Schools;
use App\Models\v1\Subject;
use App\Models\v1\SchoolBudget;
use App\Models\v1\SchoolSubjectBudget;
use App\Models\School\SchoolClassBudget;
use App\OnboardingInstructor;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class RequirementsController extends Controller
{
    public function getPostUtilities() {
        $school = auth()->user()->school;
        $schools = Schools::where(['district_id' => $school->district_id ])->get();
        $school_calender = PlatformSchoolCalendersModel::where('school_id', $school->id)->where('type', 'school')->first();
        $district_calender = PlatformSchoolCalendersModel::where('school_id', $school->id)->where('type', 'district')->first();
        return ApiResponse::success([
            'schools' => $schools,
            'school_calender' => $school_calender,
            'district_calender' => $district_calender
        ], "Post Utilities Fetched Successfully");
    }

    public function duplicateRequirement($id) {

    }

    // *************Review-Applicant-List*************
    public function reviewApplicantList($id) {
        $educators = OnboardingInstructor::with(['step1', 'step2.education', 'step2.teching', 'step2.otherExper', 'step2.references', 'step3', 'step3.subjects', 'step5', 'step6', 'shortlist'])->where('user_status', 'Active')->orderBy('id', 'DESC')->get();
        return ApiResponse::success($educators, "Educators Fetched Successfully");
    }
    // *************Review-Applicant-List*************
}