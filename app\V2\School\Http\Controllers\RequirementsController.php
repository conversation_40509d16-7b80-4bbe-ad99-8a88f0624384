<?php

namespace App\V2\School\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\PlatformSchoolRequirements;
use App\V2\Core\Helpers\ApiResponse;
use App\Schools;
use App\Models\v1\Subject;
use App\Models\v1\SchoolBudget;
use App\Models\v1\SchoolSubjectBudget;
use App\Models\School\SchoolClassBudget;
use App\Models\SchoolReviewApplicants;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class RequirementsController extends Controller
{
    public function getPostUtilities() {
        $school = auth()->user()->school;
        $schools = Schools::where(['district_id' => $school->district_id ])->get();
        $school_calender = PlatformSchoolCalendersModel::where('school_id', $school->id)->where('type', 'school')->first();
        $district_calender = PlatformSchoolCalendersModel::where('school_id', $school->id)->where('type', 'district')->first();
        return ApiResponse::success([
            'schools' => $schools,
            'school_calender' => $school_calender,
            'district_calender' => $district_calender
        ], "Post Utilities Fetched Successfully");
    }

    public function duplicateRequirement($id) {

    }

    // *************Review-Applicant-List*************
    public function reviewApplicantList($id) {
        try {
            $requirement = PlatformSchoolRequirements::find($id);
            if (!$requirement) {
                return ApiResponse::error("Requirement not found", 404);
            }
            $applicants = SchoolReviewApplicants::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6', 'user.shortList', 'school', 'requirement'])->where('requirement_id', $id)->where('school_id', auth()->user()->id)->whereHas('user')->where(function ($query) {
                $query->whereDoesntHave('user.shortList')
                    ->orWhereHas('user.shortList', function ($subQuery) {
                        $subQuery->where('status', '>', 0);
                    });
            })->orderBy('id', 'DESC')->get();

            $educatorsData = [];
            foreach ($applicants as $applicant) {
                if (!$applicant->user) continue;
                $educator = $applicant->user;

                $specialEducation = '';
                $educationList = [];
                if (!empty($educator->step2) && !empty($educator->step2->education)) {
                    foreach ($educator->step2->education as $certificate) {
                        if (!empty($certificate->education) && $certificate->education != 'Other') {
                            $educationList[] = $certificate->education;
                        } else {
                            $educationList[] = $certificate->certification_other;
                        }
                    }
                    $specialEducation = implode(',', $educationList);
                }

                $subjectsFormatted = [];
                if (!empty($educator->step3) && !empty($educator->step3->subjects)) {
                    foreach ($educator->step3->subjects as $subject) {
                        if (!empty($subject->subject)) {
                            $subjectArea = v1SubjectAreaName($subject->subject);
                            $subjectName = !empty($subject->sub_subject) ? v1SubjectName($subject->sub_subject) : '';
                            $subjectsFormatted[] = [
                                'subject_area_id' => $subject->subject,
                                'subject_area_name' => $subjectArea,
                                'sub_subject_id' => $subject->sub_subject,
                                'sub_subject_name' => $subjectName,
                                'formatted' => $subjectArea . ($subjectName ? ' - ' . $subjectName : '')
                            ];
                        }
                    }
                }

                $gradeLevels = '';
                $gradeLevelsList = [];
                if (!empty($educator->step3) && !empty($educator->step3->i_prefer_to_teach)) {
                    $gradeLevels = rtrim(gradeLevel($educator->step3->i_prefer_to_teach), ',');
                    $gradeLevelsList = explode(',', $gradeLevels);
                    $gradeLevelsList = array_map('trim', array_filter($gradeLevelsList));
                }

                $certificationStates = [];
                if (!empty($educator->step2) && !empty($educator->step2->education)) {
                    foreach ($educator->step2->education as $education) {
                        if (!empty($education->states)) {
                            $states = json_decode($education->states, true);
                            if (is_array($states)) {
                                $certificationStates = array_merge($certificationStates, array_map(function($state) {
                                    return str_replace('_', ' ', $state);
                                }, $states));
                            }
                        }
                    }
                }

                $isLiked = false;
                $isDisliked = false;
                if (!empty($educator->shortList)) {
                    foreach ($educator->shortList as $shortList) {
                        if ($shortList->status === 1 && $shortList->requirement_id == $id) {
                            $isLiked = true;
                        }
                        if ($shortList->status === 0 && $shortList->requirement_id == $id) {
                            $isDisliked = true;
                        }
                    }
                }

                $educatorData = [
                    'id' => $educator->id,
                    'applicant_id' => $applicant->id,
                    'name' => $educator->first_name . ' ' . $educator->last_name,
                    'first_name' => $educator->first_name,
                    'last_name' => $educator->last_name,
                    'email' => $educator->email,
                    'image' => $educator->image ? generateSignedUrl($educator->image) : null,

                    // Address information
                    'city' => $educator->city,
                    'state' => $educator->state,
                    'zipcode' => $educator->zipcode,
                    'full_address' => (!empty($educator->city) ? $educator->city . ', ' : '') . $educator->state,

                    // Video information
                    'video' => [
                        'video_url' => $educator->step5 && $educator->step5->video ? generateSignedUrl($educator->step5->video) : null,
                        'video_source' => $educator->step5 ? $educator->step5->video_source : null,
                        'processing_video' => $educator->step5 ? $educator->step5->processing_video : null
                    ],

                    // Profile information
                    'profile_tags' => $educator->step5 ? $educator->step5->profile_tags : null,
                    'profile_tags_array' => $educator->step5 && $educator->step5->profile_tags ?
                        explode(',', $educator->step5->profile_tags) : [],
                    'profile_title' => $educator->step5 ? $educator->step5->profile_title : null,
                    'profile_description' => $educator->step5 ? $educator->step5->description : null,

                    // Education and certification
                    'special_education' => $specialEducation,
                    'education_list' => $educationList,
                    'certification_states' => $certificationStates,
                    'certification_states_formatted' => implode(', ', $certificationStates),

                    // Grade levels
                    'grade_levels' => $gradeLevels,
                    'grade_levels_array' => $gradeLevelsList,
                    'i_prefer_to_teach' => $educator->step3 ? $educator->step3->i_prefer_to_teach : null,

                    // Subjects
                    'subjects' => $subjectsFormatted,
                    'subjects_formatted' => implode(', ', array_column($subjectsFormatted, 'formatted')),

                    // Experience
                    'years_of_experience' => $educator->step2 ? $educator->step2->total_experience : null,
                    'total_experience' => $educator->step2 ? $educator->step2->total_experience : null,

                    // Teaching format
                    'teaching_format' => $educator->step3 ? $educator->step3->format : null,

                    // Like/Dislike status
                    'is_liked' => $isLiked,
                    'is_disliked' => $isDisliked,
                    'shortlist_status' => [
                        'is_liked' => $isLiked,
                        'is_disliked' => $isDisliked
                    ],

                    // Budget and rates
                    'proposed_rate' => $applicant->proposed_rate,
                    'total_estimated_cost' => (float) $requirement->totalHours * $applicant->proposed_rate,
                    'online_rate' => $educator->onlinerate,
                    'inperson_rate' => $educator->inpersonrate,

                    // Application details
                    'application_status' => $applicant->status,
                    'created_at' => $applicant->created_at,
                    'is_new' => Carbon::parse($applicant->created_at)->isToday(),

                    // User status
                    'user_status' => $educator->user_status
                ];

                $educatorsData[] = $educatorData;
            }

            // Calculate totals like in SchoolController2
            $totalApplicants = count($educatorsData);
            $totalCost = array_sum(array_column($educatorsData, 'total_estimated_cost'));

            return ApiResponse::success([
                'requirement' => [
                    'id' => $requirement->id,
                    'requirement_name' => $requirement->requirement_name,
                    'title' => $requirement->title,
                    'school_id' => $requirement->school_id,
                    'totalHours' => $requirement->totalHours
                ],
                'applicants' => $educatorsData,
                'total_applicants' => $totalApplicants,
                'total_cost' => $totalCost
            ], "Educators Fetched Successfully");

        } catch (\Exception $e) {
            return ApiResponse::error("Failed to fetch educators: " . $e->getMessage(), 500);
        }
    }
    // *************Review-Applicant-List*************
}