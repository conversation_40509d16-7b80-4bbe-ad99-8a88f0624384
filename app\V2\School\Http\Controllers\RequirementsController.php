<?php

namespace App\V2\School\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\PlatformSchoolRequirements;
use App\V2\Core\Helpers\ApiResponse;
use App\Schools;
use App\Models\v1\Subject;
use App\Models\v1\SchoolBudget;
use App\Models\v1\SchoolSubjectBudget;
use App\Models\School\SchoolClassBudget;
use App\Models\SchoolReviewApplicants;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class RequirementsController extends Controller
{
    public function getPostUtilities() {
        $school = auth()->user()->school;
        $schools = Schools::where(['district_id' => $school->district_id ])->get();
        $school_calender = PlatformSchoolCalendersModel::where('school_id', $school->id)->where('type', 'school')->first();
        $district_calender = PlatformSchoolCalendersModel::where('school_id', $school->id)->where('type', 'district')->first();
        return ApiResponse::success([
            'schools' => $schools,
            'school_calender' => $school_calender,
            'district_calender' => $district_calender
        ], "Post Utilities Fetched Successfully");
    }

    public function duplicateRequirement($id) {

    }

    // *************Review-Applicant-List*************
    public function reviewApplicantList($id) {
        try {
            // Get the requirement details
            $requirement = PlatformSchoolRequirements::find($id);
            // dd($requirement);
            if (!$requirement) {
                return ApiResponse::error("Requirement not found", 404);
            }

            // Get applicants for this specific requirement
            $applicants = SchoolReviewApplicants::with([
                'user.step1',
                'user.step2.education',
                'user.step2.teching',
                'user.step2.otherExper',
                'user.step2.references',
                'user.step3',
                'user.step3.subjects',
                'user.step5',
                'user.step6',
                'user.shortList',
                'user.approvedBudget.lines',
                'user.googleMap'
            ])
            ->where('requirement_id', $id)
            ->where('school_id', 1)
            ->get();
            // dd($applicants);
            $educatorsData = [];

            foreach ($applicants as $applicant) {
                if (!$applicant->user) continue;

                $educator = $applicant->user;

                // Get like/dislike status for this requirement
                $shortlistStatus = $educator->shortList()
                    ->where('requirement_id', $id)
                    ->where('school_id', auth()->user()->id)
                    ->first();

                // Build educator data
                $educatorData = [
                    'id' => $educator->id,
                    'name' => trim($educator->first_name . ' ' . $educator->last_name),
                    'email' => $educator->email,
                    'image' => $educator->image ? generateSignedUrl($educator->image) : null,

                    // Address information
                    'address' => [
                        'city' => $educator->city,
                        'state' => $educator->state,
                        'zipcode' => $educator->zipcode,
                        'full_address' => trim(($educator->city ? $educator->city . ', ' : '') . $educator->state)
                    ],

                    // Video information
                    'video' => [
                        'video_url' => $educator->step5 ? $educator->step5->video : null,
                        'video_source' => $educator->step5 ? $educator->step5->video_source : null,
                        'processing_video' => $educator->step5 ? $educator->step5->processing_video : null
                    ],

                    // Profile tags
                    'tags' => $educator->step5 ? $educator->step5->profile_tags : null,

                    // Grade levels
                    'grade_levels' => $educator->step3 ? $educator->step3->grade_level_names : [],

                    // Subjects
                    'subjects' => $educator->step3 && $educator->step3->subjects ?
                        $educator->step3->subjects->map(function($subject) {
                            return [
                                'id' => $subject->sub_subject,
                                'name' => $subject->subSubject ? $subject->subSubject->subject_name : null
                            ];
                        }) : [],

                    // Years of experience
                    'years_of_experience' => $educator->step2 ? $educator->step2->total_experience : null,

                    // Education/Certifications
                    'education' => $educator->step2 && $educator->step2->education ?
                        $educator->step2->education->map(function($edu) {
                            return $edu->education !== 'Other' ? $edu->education : $edu->certification_other;
                        })->filter()->implode(', ') : null,

                    // Budget information
                    'budget' => [
                        'online_rate' => $educator->onlinerate,
                        'inperson_rate' => $educator->inpersonrate,
                        'approved_budget' => $educator->approvedBudget->isNotEmpty() ? [
                            'in_person' => $educator->approvedBudget->first()->in_person,
                            'bilingual_inc' => $educator->approvedBudget->first()->bilingual_inc,
                            'case_management' => $educator->approvedBudget->first()->case_management,
                            'budget_lines' => $educator->approvedBudget->first()->lines->map(function($line) {
                                return [
                                    'subject_code' => $line->subject_code,
                                    'subject_title' => $line->subject_title,
                                    'base_pay' => $line->base_pay,
                                    'experience_pay' => $line->experience_pay,
                                    'education_pay' => $line->education_pay,
                                    'non_teaching' => $line->non_teaching,
                                    'special_education' => $line->special_education,
                                    'total' => $line->total
                                ];
                            })
                        ] : null
                    ],

                    // Like/Dislike status
                    'shortlist_status' => [
                        'is_liked' => $shortlistStatus && $shortlistStatus->status == 1,
                        'is_disliked' => $shortlistStatus && $shortlistStatus->status == 0,
                        'status_value' => $shortlistStatus ? $shortlistStatus->status : null
                    ],

                    // Additional profile information
                    'profile' => [
                        'title' => $educator->step5 ? $educator->step5->profile_title : null,
                        'description' => $educator->step5 ? $educator->step5->description : null,
                        'teaching_format' => $educator->step3 ? $educator->step3->format : null,
                        'user_status' => $educator->user_status
                    ],

                    // Proposed rate for this application
                    'proposed_rate' => $applicant->proposed_rate,
                    'application_status' => $applicant->status
                ];

                $educatorsData[] = $educatorData;
            }

            return ApiResponse::success([
                'requirement' => [
                    'id' => $requirement->id,
                    'title' => $requirement->title,
                    'school_id' => $requirement->school_id
                ],
                'educators' => $educatorsData,
                'total_count' => count($educatorsData)
            ], "Educators Fetched Successfully");

        } catch (\Exception $e) {
            return ApiResponse::error("Failed to fetch educators: " . $e->getMessage(), 500);
        }
    }
    // *************Review-Applicant-List*************
}