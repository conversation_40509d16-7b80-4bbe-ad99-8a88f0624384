<?php

namespace App\V2\School\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\PlatformSchoolRequirements;
use App\V2\Core\Helpers\ApiResponse;
use App\Schools;
use App\Models\SchoolReviewApplicants;
use Exception;

class RequirementsController extends Controller
{
    public function getPostUtilities() {
        $school = auth()->user()->school;
        $schools = Schools::where(['district_id' => $school->district_id ])->get();
        $school_calender = PlatformSchoolCalendersModel::where('school_id', $school->id)->where('type', 'school')->first();
        $district_calender = PlatformSchoolCalendersModel::where('school_id', $school->id)->where('type', 'district')->first();
        return ApiResponse::success([
            'schools' => $schools,
            'school_calender' => $school_calender,
            'district_calender' => $district_calender
        ], "Post Utilities Fetched Successfully");
    }

    public function duplicateRequirement($id) {

    }

    // *************Review-Applicant-List*************
    public function reviewApplicantList($id)
    {
        try {
            $requirement = PlatformSchoolRequirements::find($id);
            if (!$requirement) {
                return ApiResponse::error("Requirement not found", 404);
            }
            $applicants = SchoolReviewApplicants::with(['user.step1', 'user.step2.education', 'user.step2.teching', 'user.step3.subjects', 'user.step5', 'user.step6', 'user.shortList', 'school', 'requirement'])->where('requirement_id', $id)->where('school_id', auth()->user('school_id'))->whereHas('user')->where(function ($query) {
                $query->whereDoesntHave('user.shortList')
                    ->orWhereHas('user.shortList', function ($subQuery) {
                        $subQuery->where('status', '>', 0);
                    });
            })->orderBy('id', 'DESC')->get();

            $educatorsData = [];
            foreach ($applicants as $applicant) {
                if (!$applicant->user) continue;
                $educator = $applicant->user;

                // Process education data
                $educationList = [];
                if ($educator->step2 && $educator->step2->education) {
                    foreach ($educator->step2->education as $certificate) {
                        $educationList[] = [
                            'education' => $certificate->education,
                            'certification_other' => $certificate->certification_other,
                            'states' => $certificate->states ? json_decode($certificate->states, true) : []
                        ];
                    }
                }

                // Process subjects data
                $subjects = [];
                if ($educator->step3 && $educator->step3->subjects) {
                    foreach ($educator->step3->subjects as $subject) {
                        $subjects[] = [
                            'subject_area_id' => $subject->subject,
                            'sub_subject_id' => $subject->sub_subject,
                            'subject_area_name' => $subject->subject ? v1SubjectAreaName($subject->subject) : null,
                            'sub_subject_name' => $subject->sub_subject ? v1SubjectName($subject->sub_subject) : null
                        ];
                    }
                }

                // Process grade levels
                $gradeLevels = [];
                if ($educator->step3 && $educator->step3->i_prefer_to_teach) {
                    $gradeIds = explode(',', $educator->step3->i_prefer_to_teach);
                    foreach ($gradeIds as $gradeId) {
                        $gradeLevels[] = [
                            'id' => trim($gradeId),
                            'name' => gradeLevel(trim($gradeId))
                        ];
                    }
                }

                // Check shortlist status
                $shortlistStatus = null;
                if ($educator->shortList) {
                    foreach ($educator->shortList as $shortList) {
                        if ($shortList->requirement_id == $id) {
                            $shortlistStatus = $shortList->status;
                            break;
                        }
                    }
                }

                // Build clean educator data structure
                $educatorData = [
                    // Basic Information
                    'id' => $educator->id,
                    'applicant_id' => $applicant->id,
                    'first_name' => $educator->first_name,
                    'last_name' => $educator->last_name,
                    'email' => $educator->email,
                    'image' => $educator->image ? generateSignedUrl($educator->image) : null,
                    'user_status' => $educator->user_status,

                    // Location
                    'address' => [
                        'city' => $educator->city,
                        'state' => $educator->state,
                        'zipcode' => $educator->zipcode
                    ],

                    // Profile
                    'profile' => [
                        'title' => $educator->step5 ? $educator->step5->profile_title : null,
                        'description' => $educator->step5 ? $educator->step5->description : null,
                        'tags' => $educator->step5 && $educator->step5->profile_tags ? explode(',', $educator->step5->profile_tags) : [],
                        'video' => [
                            'url' => $educator->step5 && $educator->step5->video ? generateSignedUrl($educator->step5->video) : null,
                            'source' => $educator->step5 ? $educator->step5->video_source : null,
                            'processing' => $educator->step5 ? $educator->step5->processing_video : null
                        ]
                    ],

                    // Education & Certifications
                    'education' => $educationList,

                    // Teaching Preferences
                    'teaching_preferences' => [
                        'grade_levels' => $gradeLevels,
                        'subjects' => $subjects,
                        'format' => $educator->step3 ? $educator->step3->format : null,
                        'years_of_experience' => $educator->step2 ? $educator->step2->total_experience : null
                    ],

                    // Rates & Budget
                    'rates' => [
                        'proposed_rate' => $applicant->proposed_rate,
                        'online_rate' => $educator->onlinerate,
                        'inperson_rate' => $educator->inpersonrate,
                        'total_estimated_cost' => (float) $requirement->totalHours * $applicant->proposed_rate
                    ],

                    // Application Status
                    'application' => [
                        'status' => $applicant->status,
                        'created_at' => $applicant->created_at,
                        'shortlist_status' => $shortlistStatus // null, 0 (disliked), or 1 (liked)
                    ]
                ];
                $educatorsData[] = $educatorData;
            }

            // Calculate totals
            $totalApplicants = count($educatorsData);
            $totalCost = 0;
            foreach ($educatorsData as $educator) {
                $totalCost += $educator['rates']['total_estimated_cost'];
            }

            return ApiResponse::success([
                'requirement' => [
                    'id' => $requirement->id,
                    'requirement_name' => $requirement->requirement_name,
                    'title' => $requirement->title,
                    'school_id' => $requirement->school_id,
                    'total_hours' => $requirement->totalHours
                ],
                'applicants' => $educatorsData,
                'summary' => [
                    'total_applicants' => $totalApplicants,
                    'total_estimated_cost' => $totalCost
                ]
            ], "Educators Fetched Successfully");
        } catch (Exception $e) {
            return ApiResponse::error("Failed to fetch educators: " . $e->getMessage(), 500);
        }
    }
    // *************Review-Applicant-List*************
}