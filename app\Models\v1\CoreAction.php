<?php

namespace App\Models\V1;

use Illuminate\Database\Eloquent\Model;

class CoreAction extends Model
{
    protected $table = 'core_actions';

    protected $fillable = [
        'action_key',
        'description',
    ];

    // Relationships
    public function roles()
    {
        return $this->belongsToMany(SchoolRole::class, 'school_role_actions', 'action_id', 'role_id');
    }

    public function users()
    {
        return $this->belongsToMany(SchoolUser::class, 'school_user_actions', 'action_id', 'user_id');
    }
}
