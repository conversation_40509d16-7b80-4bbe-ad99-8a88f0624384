<?php

use Illuminate\Support\Facades\Route;
use App\V2\School\Http\Controllers\{AuthController, CalculateBudgetController};


#region Open Routes
Route::group([
    'prefix' => 'schools',
    'as' => 'school.',
], function () {
    Route::post('login', [AuthController::class, 'login'])->name('login');
});

#region Auth Required
Route::group([
    'prefix' => 'schools',
    'as' => 'school.',
    'middleware' => ['CheckSchoolSession', 'auth:platform_school'],
], function () {
    // User Profile
    Route::get('profile', [AuthController::class, 'profile'])->name('profile');

    #region Calculate budget
    Route::get('subject-budgets/{subjectId}', [CalculateBudgetController::class, 'bySubject'])->name('getAllBudgets');
    Route::get('all-budgets', [CalculateBudgetController::class, 'fetchAllSchoolBudget'])->name('fetchAllSchoolBudget');
    Route::post('calculate-budgets', [CalculateBudgetController::class, 'store'])->name('calculate-budgets.store');
    Route::put('calculate-budgets/{id}', [CalculateBudgetController::class, 'update'])->name('calculate-budgets.update');
    Route::delete('calculate-budgets/{id}/delete', [CalculateBudgetController::class, 'destroy'])->name('calculate-budgets.destroy');
    Route::get('calculate-budgets/{id}/duplicate', [CalculateBudgetController::class, 'duplicate_budget'])->name('calculate-budgets.duplicate');
    #endregion

    #region Post Requirement

});