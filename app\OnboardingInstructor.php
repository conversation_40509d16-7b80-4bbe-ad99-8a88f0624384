<?php

namespace App;

use App\OnboardingInstructorLocation;
use App\Models\InstructorBudgetApprovedModel;
use App\Models\InstructorBudgetLine;
use App\Models\OnboardingInstructorContract;
use App\Models\OnboardingInstructorMarketplaceContract;
use App\Models\k12ConnectionClasses;
use App\Models\v1\UserAdditionalSubject;
use App\Models\v1\UserAvailability;
use App\Models\v1\UserNotificationPreference;
use App\Models\v1\UserOnboardingFinalization;
use App\Models\v1\UserOpportunityPreference;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;

class OnboardingInstructor extends Authenticatable
{
    use SoftDeletes;
    protected $table = 'new_onboarding_instructor';
    protected $fillable = [
        'user_status',
        'application_start_date',
        'onlinerate',
        'inpersonrate',
        'educator',
        'isDeclineContract',
        'seen_count',
        'is_background_check',
        'temp_password_changed',
    ];

    public function googleMap()
    {
        return $this->hasOne(OnboardingIinstructorGoogleMapAddress::class, 'instructor_onboarding_id', 'id');
    }

    public function step1()
    {
        return $this->hasOne(InstructorFirstStepOnboardingModel::class, 'user_id', 'id');
    }

    public function step2()
    {
        return $this->hasOne(InstructorSecondStepOnboardingModel::class, 'user_id', 'id');
    }

    public function step3()
    {
        return $this->hasOne(InstructorThirdStepOnboardingModel::class, 'user_id', 'id');
    }

    public function step5()
    {
        return $this->hasOne(InstructorFifthStepOnboardingModel::class, 'user_id', 'id');
    }

    public function step6()
    {
        return $this->hasOne(InstructorSixthStepOnboardingModel::class, 'user_id', 'id');
    }

    public function step7()
    {
        return $this->hasOne(InstructorSeventhStepOnboardingModel::class, 'user_id', 'id');
    }

    public function rejectInstructor()
    {
        return $this->hasOne(RejectOnboardingInstructorModel::class, 'onboarding_instructor_id', 'id');
    }

    public function notifications()
    {
        return $this->hasMany(notification::class, 'user_id');
    }

    public function unreadNotifications()
    {
        return $this->notifications()->unread()->count();
    }

    public function shortList()
    {
        return $this->hasMany(ShortlistInstructorModel::class, 'user_id', 'id');
    }

    public function whizaraContract()
    {
        return $this->hasOne(OnboardingInstructorContract::class, 'user_id', 'id');
    }

    public function marketplaceContract()
    {
        return $this->hasOne(OnboardingInstructorMarketplaceContract::class, 'user_id', 'id');
    }


    public function category(){

        return $this->hasMany(PlatformInstructorCategory::class,"instructor_id","id");
    }

    public function approvedBudget()
    {
        return $this->hasMany(InstructorBudgetApprovedModel::class, 'user_id');
    }

    public function budgetLines()
    {
        return $this->hasManyThrough(
            InstructorBudgetLine::class,
            InstructorBudgetApprovedModel::class,
            'user_id',     // FK on instructor_budget_approved
            'approved_id', // FK on instructor_budget_lines
            'id',          // PK on onboarding_instructors
            'id'           // PK on instructor_budget_approved
        );
    }


    public function classesAsMainInstructor()
    {
        return $this->hasMany(k12ConnectionClasses::class, 'main_instructor_id', 'id')
            ->whereNull('sub_instructor_id');
    }

    public function classesAsSubInstructor()
    {
        return $this->hasMany(k12ConnectionClasses::class, 'sub_instructor_id', 'id');
    }

    public function assignedClasses()
    {
        $instructorId = $this->id;
        return $this->hasMany(k12ConnectionClasses::class, 'main_instructor_id', 'id')
            ->where(function($q) use ($instructorId) {
                $q->where(function($q) use ($instructorId) {
                    $q->where('main_instructor_id', $instructorId)
                      ->whereNull('sub_instructor_id');
                })->orWhere('sub_instructor_id', $instructorId);
            });
    }

    public function hasAvailability($day, $start, $end)
    {
        $start = date("H:i:s", strtotime($start)); // "00:00:00"
        $end   = date("H:i:s", strtotime($end));   // "01:00:00"
        return $this->availability()
            ->where('day_of_week', strtolower($day))
            ->where(function($q) use ($start, $end) {
                $q->whereBetween('start_time', [$start, $end])
                ->orWhereBetween('end_time', [$start, $end]);
            })
            ->exists();
    }


    public function availability()
    {
        return $this->hasMany(UserAvailability::class, 'user_id');
    }

    public function additionalSubjects()
    {
        return $this->hasMany(UserAdditionalSubject::class, 'user_id');
    }

    public function notificationPreference()
    {
        return $this->hasMany(UserNotificationPreference::class, 'user_id');
    }

    public function locations()
    {
        return $this->hasOne(OnboardingInstructorLocation::class, 'user_id');
    }

    public function onboardingFinalization()
    {
        return $this->hasOne(UserOnboardingFinalization::class, 'user_id');
    }

    public function opportunityPreferences()
    {
        return $this->hasMany(UserOpportunityPreference::class, 'instructor_id');
    }

    public function savedOpportunities()
    {
        return $this->opportunityPreferences()->where('status', 'saved');
    }

    public function archivedOpportunities()
    {
        return $this->opportunityPreferences()->where('status', 'archived');
    }
}
